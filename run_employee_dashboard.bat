@echo off
chcp 65001 >nul
title 👷‍♂️ برنامج إدارة الحلاقة - لوحة تحكم العامل
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              💈 برنامج إدارة الحلاقة 💈                    ██
echo ██                   لوحة تحكم العامل                        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    pause
    exit /b 1
)

echo 👷‍♂️ تشغيل البرنامج مع لوحة تحكم العامل المخصصة...
echo.
echo ✨ مكونات لوحة تحكم العامل:
echo    🏠 رأس مخصص بلون أخضر
echo    👋 رسالة ترحيب شخصية
echo    📊 8 إحصائيات شخصية للعامل
echo    🕐 عمليات العامل الحديثة
echo    ⚡ 6 إجراءات سريعة مخصصة
echo    💡 نصائح وإرشادات مهنية
echo.
echo 📊 الإحصائيات الشخصية:
echo    💺 إجمالي عملياتك المنجزة
echo    💰 إجمالي أرباحك المحققة
echo    📅 عمليات اليوم الحالي
echo    🏆 أرباح اليوم الحالي
echo    📈 متوسط العمليات اليومية
echo    ⭐ تقييمك من الزبائن
echo    🎯 هدف اليوم (10 عمليات)
echo    🏅 مستوى أدائك العام
echo.
echo ⚡ الإجراءات السريعة للعامل:
echo    💺 تسجيل عملية جديدة
echo    👤 إضافة زبون جديد
echo    📊 عرض عملياتي وأرباحي
echo    👥 قائمة جميع الزبائن
echo    ✂️ قائمة الخدمات المتاحة
echo    🏆 عرض إنجازاتي وتقييماتي
echo.
echo 🏆 نظام الإنجازات:
echo    🎯 البداية - أول عملية
echo    ⭐ نجم صاعد - 10 عمليات
echo    🏅 محترف - 25 عملية
echo    👑 خبير - 50 عملية
echo    💰 مربح - 500 ج.م أرباح
echo    💎 ماسي - 1000 ج.م أرباح
echo.
echo 💡 النصائح المهنية:
echo    🎯 اهدف لإنجاز 10 عمليات يومياً
echo    😊 ابتسم واستقبل الزبائن بود
echo    ✂️ حافظ على نظافة أدواتك
echo    ⏰ احترم مواعيد الزبائن
echo    🎨 تعلم تقنيات جديدة
echo    💬 استمع لطلبات الزبائن
echo.
echo 🕐 عملياتك الحديثة:
echo    • عرض آخر 5 عمليات شخصية
echo    • تفاصيل كاملة لكل عملية
echo    • تقييم وهمي ⭐⭐⭐⭐⭐
echo    • ألوان متناوبة للوضوح
echo.
echo 📱 الصفحات المخصصة:
echo    📊 صفحة "عملياتي" - جدول مفصل
echo    🏆 صفحة "إنجازاتي" - نظام التحفيز
echo.
echo 🔐 طريقة الدخول:
echo    👷‍♂️ اضغط على بطاقة أي عامل
echo    📊 ستظهر لوحة التحكم المخصصة
echo.
echo 💡 نصائح الاستخدام:
echo    • راجع إحصائياتك يومياً
echo    • اهدف لتحقيق 10 عمليات يومياً
echo    • تابع إنجازاتك واسع للمزيد
echo    • اقرأ النصائح وطبقها
echo    • استخدم الإجراءات السريعة
echo.
echo 🌟 رسالة تحفيزية:
echo    "أنت جزء مهم من فريق الصالون"
echo    "عملك الجيد يساهم في نجاحنا جميعاً!"
echo.
echo ═══════════════════════════════════════
echo.

REM تشغيل البرنامج
python clean_run.py

REM في حالة إغلاق البرنامج
echo.
echo 👷‍♂️ شكراً لاستخدام لوحة تحكم العامل!
echo 💈 نأمل أن تكون قد استفدت من المعلومات الشخصية!
echo 🌟 استمر في العمل الجيد وحقق المزيد من الإنجازات!
echo.
pause
