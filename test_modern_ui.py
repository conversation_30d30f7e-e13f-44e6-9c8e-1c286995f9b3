#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التصميم العصري لبرنامج إدارة الحلاقة
"""

import sys
import os

def test_modern_theme():
    """اختبار الثيم العصري"""
    print("🎨 اختبار الثيم العصري...")
    
    try:
        from modern_theme import modern_theme
        
        # اختبار الألوان
        colors = modern_theme.colors
        print(f"✅ تم تحميل {len(colors)} لون")
        
        # اختبار الخطوط
        fonts = modern_theme.fonts
        print(f"✅ تم تحميل {len(fonts)} خط")
        
        # اختبار الأيقونات
        icons = modern_theme.get_barber_icons()
        print(f"✅ تم تحميل {len(icons)} أيقونة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الثيم العصري: {e}")
        return False

def test_ui_enhancements():
    """اختبار تحسينات الواجهة"""
    print("\n🔧 اختبار تحسينات الواجهة...")
    
    try:
        from ui_enhancements import ui_enhancements
        print("✅ تم تحميل تحسينات الواجهة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحسينات الواجهة: {e}")
        return False

def test_main_app_with_modern_ui():
    """اختبار التطبيق الرئيسي مع الواجهة العصرية"""
    print("\n🚀 اختبار التطبيق مع الواجهة العصرية...")
    
    try:
        # اختبار الاستيراد
        from main import CoiffeurApp
        print("✅ تم استيراد التطبيق الرئيسي")
        
        # اختبار إنشاء التطبيق (بدون تشغيل)
        app = CoiffeurApp()
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # اختبار الألوان والخطوط
        if hasattr(app, 'colors') and hasattr(app, 'fonts'):
            print("✅ تم تحميل الألوان والخطوط")
        else:
            print("⚠️  الألوان والخطوط غير محملة")
        
        # اختبار الأيقونات
        if hasattr(app, 'barber_icons'):
            print(f"✅ تم تحميل {len(app.barber_icons)} أيقونة حلاقة")
        else:
            print("⚠️  أيقونات الحلاقة غير محملة")
        
        # إغلاق التطبيق
        app.root.destroy()
        print("✅ تم إغلاق التطبيق بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التطبيق: {e}")
        return False

def test_database_with_ui():
    """اختبار قاعدة البيانات مع الواجهة"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database import Database
        
        # إنشاء قاعدة بيانات تجريبية
        db = Database("test_modern_ui.db")
        
        # اختبار البيانات الأساسية
        customers = db.get_customers()
        services = db.get_services()
        operations = db.get_operations()
        
        print(f"✅ الزبائن: {len(customers)}")
        print(f"✅ الخدمات: {len(services)}")
        print(f"✅ العمليات: {len(operations)}")
        
        # حذف قاعدة البيانات التجريبية
        if os.path.exists("test_modern_ui.db"):
            os.remove("test_modern_ui.db")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_translations():
    """اختبار نظام الترجمات"""
    print("\n🌐 اختبار نظام الترجمات...")
    
    try:
        from translations import Translator
        
        # اختبار الترجمة العربية
        ar_translator = Translator('ar')
        ar_text = ar_translator.get('login')
        print(f"✅ العربية: {ar_text}")
        
        # اختبار الترجمة الفرنسية
        fr_translator = Translator('fr')
        fr_text = fr_translator.get('login')
        print(f"✅ الفرنسية: {fr_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الترجمات: {e}")
        return False

def run_visual_test():
    """تشغيل اختبار بصري للواجهة"""
    print("\n👁️ اختبار بصري للواجهة...")
    
    try:
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        from modern_theme import modern_theme
        
        # إنشاء نافذة اختبار
        root = ttk_bs.Window(themename="superhero")
        root.title("🎨 اختبار التصميم العصري")
        root.geometry("800x600")
        root.configure(bg=modern_theme.colors['bg_primary'])
        
        # إطار رئيسي
        main_frame = ttk_bs.Frame(root, style='Card.TFrame')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # عنوان
        title = ttk_bs.Label(
            main_frame,
            text="💈 اختبار التصميم العصري",
            font=modern_theme.fonts['title_large'],
            bootstyle="primary"
        )
        title.pack(pady=20)
        
        # بطاقات الألوان
        colors_frame = ttk_bs.Frame(main_frame)
        colors_frame.pack(fill='x', pady=10)
        
        color_samples = [
            ("primary", "الأساسي"),
            ("secondary", "الثانوي"),
            ("success", "النجاح"),
            ("warning", "التحذير"),
            ("danger", "الخطر"),
            ("info", "المعلومات")
        ]
        
        for color, name in color_samples:
            btn = ttk_bs.Button(
                colors_frame,
                text=f"🎨 {name}",
                bootstyle=color,
                width=12
            )
            btn.pack(side='left', padx=5, pady=5)
        
        # أيقونات الحلاقة
        icons_frame = ttk_bs.LabelFrame(main_frame, text="🔧 أيقونات الحلاقة", padding=15)
        icons_frame.pack(fill='x', pady=10)
        
        icons_text = " ".join([
            modern_theme.get_barber_icons()['scissors'],
            modern_theme.get_barber_icons()['razor'],
            modern_theme.get_barber_icons()['comb'],
            modern_theme.get_barber_icons()['chair'],
            modern_theme.get_barber_icons()['mirror'],
            modern_theme.get_barber_icons()['money'],
            modern_theme.get_barber_icons()['calendar']
        ])
        
        icons_label = ttk_bs.Label(
            icons_frame,
            text=icons_text,
            font=('Segoe UI Emoji', 24)
        )
        icons_label.pack()
        
        # زر الإغلاق
        close_btn = ttk_bs.Button(
            main_frame,
            text="✅ الاختبار مكتمل - إغلاق",
            command=root.destroy,
            bootstyle="success",
            width=30
        )
        close_btn.pack(pady=20)
        
        print("✅ تم فتح نافذة الاختبار البصري")
        print("💡 أغلق النافذة للمتابعة...")
        
        # تشغيل النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار البصري: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار التصميم العصري - برنامج إدارة الحلاقة")
    print("=" * 60)
    
    tests = [
        ("الثيم العصري", test_modern_theme),
        ("تحسينات الواجهة", test_ui_enhancements),
        ("قاعدة البيانات", test_database_with_ui),
        ("نظام الترجمات", test_translations),
        ("التطبيق الرئيسي", test_main_app_with_modern_ui),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        
        # تشغيل الاختبار البصري
        visual_test = input("\nهل تريد تشغيل الاختبار البصري؟ (y/n): ")
        if visual_test.lower() in ['y', 'yes', 'نعم']:
            run_visual_test()
        
        print("\n✅ التصميم العصري جاهز للاستخدام!")
        print("🚀 يمكنك الآن تشغيل البرنامج: python clean_run.py")
        
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
