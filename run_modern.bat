@echo off
chcp 65001 >nul
title 💈 برنامج إدارة الحلاقة - التصميم العصري
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              💈 برنامج إدارة الحلاقة 💈                    ██
echo ██                    التصميم العصري                         ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    pause
    exit /b 1
)

echo 🎨 تشغيل البرنامج بالتصميم العصري الجديد...
echo.
echo ✨ المميزات الجديدة:
echo    • 26 لون عصري مستوحى من الحلاقة
echo    • 61 أيقونة حلاقة احترافية  
echo    • تصميم بطاقات أنيق
echo    • تأثيرات بصرية تفاعلية
echo    • واجهة حديثة وسهلة الاستخدام
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👨‍💼 المدير: admin / admin123
echo    👷‍♂️ العاملون: الاسم فقط
echo.
echo ═══════════════════════════════════════
echo.

REM تشغيل البرنامج
python clean_run.py

REM في حالة إغلاق البرنامج
echo.
echo 💈 شكراً لاستخدام برنامج إدارة الحلاقة العصري!
echo.
pause
