#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة الحلاقة
تطبيق شامل لإدارة عمليات الحلاقة والزبائن والخدمات والتقارير

المطور: Augment Agent
التاريخ: 2025
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_packages = [
        'ttkbootstrap',
        'Pillow',
        'reportlab',
        'openpyxl',
        'arabic_reshaper',
        'python_bidi'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'python_bidi':
                import bidi
            elif package == 'arabic_reshaper':
                import arabic_reshaper
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        root = tk.Tk()
        root.withdraw()
        
        message = "المكتبات التالية مفقودة ويجب تثبيتها:\n\n"
        message += "\n".join(f"- {pkg}" for pkg in missing_packages)
        message += "\n\nيرجى تشغيل الأمر التالي لتثبيت المكتبات:\n"
        message += "pip install " + " ".join(missing_packages)
        
        messagebox.showerror("مكتبات مفقودة", message)
        return False
    
    return True

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # فحص المكتبات
        if not check_dependencies():
            return
        
        # استيراد وتشغيل التطبيق
        from main import CoiffeurApp
        
        app = CoiffeurApp()
        app.run()
        
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "خطأ في التشغيل",
            f"حدث خطأ أثناء تشغيل البرنامج:\n{str(e)}"
        )

if __name__ == "__main__":
    main()
