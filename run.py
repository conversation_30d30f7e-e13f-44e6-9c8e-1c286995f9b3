#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة الحلاقة
تطبيق شامل لإدارة عمليات الحلاقة والزبائن والخدمات والتقارير

المطور: Augment Agent
التاريخ: 2025
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """فحص المكتبات المطلوبة بصمت"""
    required_packages = [
        ('ttkbootstrap', 'ttkbootstrap'),
        ('Pillow', 'PIL'),
        ('reportlab', 'reportlab'),
        ('openpyxl', 'openpyxl'),
        ('arabic_reshaper', 'arabic_reshaper'),
        ('python-bidi', 'bidi')
    ]

    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        # عرض رسالة مبسطة فقط
        root = tk.Tk()
        root.withdraw()

        message = "يتطلب البرنامج تثبيت بعض المكتبات.\n\n"
        message += "هل تريد تثبيتها تلقائياً؟\n\n"
        message += "المكتبات المطلوبة:\n"
        message += "\n".join(f"• {pkg}" for pkg in missing_packages)

        result = messagebox.askyesno("تثبيت المكتبات", message)

        if result:
            # محاولة التثبيت التلقائي
            import subprocess
            import sys

            try:
                for package in missing_packages:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])

                messagebox.showinfo("نجح التثبيت", "تم تثبيت جميع المكتبات بنجاح!\nسيتم إعادة تشغيل البرنامج.")
                return True

            except Exception as e:
                messagebox.showerror("فشل التثبيت",
                    f"فشل في تثبيت المكتبات تلقائياً.\n\n"
                    f"يرجى تشغيل الأمر التالي يدوياً:\n"
                    f"pip install {' '.join(missing_packages)}\n\n"
                    f"خطأ: {str(e)}")
                return False
        else:
            return False

    return True

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # فحص المكتبات
        if not check_dependencies():
            return
        
        # استيراد وتشغيل التطبيق
        from main import CoiffeurApp
        
        app = CoiffeurApp()
        app.run()
        
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "خطأ في التشغيل",
            f"حدث خطأ أثناء تشغيل البرنامج:\n{str(e)}"
        )

if __name__ == "__main__":
    main()
