@echo off
chcp 65001 >nul
title 💳 برنامج إدارة الحلاقة - نظام البطاقات
color 0B

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              💈 برنامج إدارة الحلاقة 💈                    ██
echo ██                    نظام البطاقات التفاعلي                 ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    pause
    exit /b 1
)

echo 💳 تشغيل البرنامج بنظام البطاقات الجديد...
echo.
echo ✨ المميزات الجديدة:
echo    💳 بطاقات ملونة لكل عامل
echo    🎨 6 أنماط ألوان مختلفة
echo    👤 10 أيقونات شخصية متنوعة
echo    📊 معلومات تفصيلية لكل عامل
echo    🏆 نظام شارات للتميز
echo    ➕ إمكانية إضافة عاملين جدد
echo    🎭 تأثيرات تمرير متقدمة
echo.
echo 👥 العاملون الافتراضيون:
echo    👨‍🦲 أحمد محمد - قص شعر كلاسيكي
echo    👨‍🦱 محمد علي - حلاقة ذقن احترافية  
echo    👨‍🦳 علي حسن - تصفيف شعر عصري
echo    👨‍🦰 حسن أحمد - قص شعر أطفال
echo    👨 سامي محمود - حلاقة تقليدية
echo    🧔 محمود سامي - تسريح مناسبات
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👨‍💼 المدير: admin / admin123
echo    👷‍♂️ العاملون: اضغط على البطاقة مباشرة
echo.
echo 💡 نصائح الاستخدام:
echo    • مرر الماوس على البطاقات لرؤية التأثيرات
echo    • لاحظ الألوان والأيقونات المختلفة
echo    • جرب إضافة عامل جديد
echo    • استكشف المعلومات التفصيلية
echo.
echo ═══════════════════════════════════════
echo.

REM تشغيل البرنامج
python clean_run.py

REM في حالة إغلاق البرنامج
echo.
echo 💳 شكراً لاستخدام نظام البطاقات التفاعلي!
echo.
pause
