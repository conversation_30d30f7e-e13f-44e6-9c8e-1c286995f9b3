from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
import os
from datetime import datetime
import arabic_reshaper
from bidi.algorithm import get_display

class ReportGenerator:
    def __init__(self, translator):
        self.translator = translator
        self.setup_arabic_font()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي للـ PDF"""
        try:
            # محاولة تسجيل خط عربي
            font_path = "arial.ttf"  # يجب وضع ملف الخط في مجلد المشروع
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                self.arabic_font = 'Arabic'
            else:
                self.arabic_font = 'Helvetica'
        except:
            self.arabic_font = 'Helvetica'
    
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح في PDF"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    def export_to_pdf(self, operations, stats, filename, title="تقرير العمليات"):
        """تصدير التقرير إلى PDF"""
        try:
            doc = SimpleDocTemplate(filename, pagesize=A4)
            story = []
            
            # الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=self.arabic_font,
                fontSize=18,
                alignment=1,  # وسط
                spaceAfter=30
            )
            
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontName=self.arabic_font,
                fontSize=12,
                alignment=2  # يمين
            )
            
            # العنوان
            title_text = self.format_arabic_text(title)
            story.append(Paragraph(title_text, title_style))
            story.append(Spacer(1, 20))
            
            # تاريخ التقرير
            date_text = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            story.append(Paragraph(self.format_arabic_text(date_text), normal_style))
            story.append(Spacer(1, 20))
            
            # الإحصائيات
            stats_data = [
                [self.format_arabic_text("البيان"), self.format_arabic_text("القيمة")],
                [self.format_arabic_text("إجمالي العمليات"), str(stats['total_operations'])],
                [self.format_arabic_text("إجمالي الإيرادات"), f"{stats['total_revenue']:.2f} ج.م"],
                [self.format_arabic_text("أرباح العاملين"), f"{stats['employee_earnings']:.2f} ج.م"],
                [self.format_arabic_text("أرباح المالك"), f"{stats['owner_earnings']:.2f} ج.م"]
            ]
            
            stats_table = Table(stats_data, colWidths=[200, 100])
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(stats_table)
            story.append(Spacer(1, 30))
            
            # جدول العمليات
            if operations:
                operations_title = self.format_arabic_text("تفاصيل العمليات")
                story.append(Paragraph(operations_title, title_style))
                story.append(Spacer(1, 10))
                
                # رؤوس الأعمدة
                headers = [
                    self.format_arabic_text("الزبون"),
                    self.format_arabic_text("الخدمة"),
                    self.format_arabic_text("نفذها"),
                    self.format_arabic_text("السعر"),
                    self.format_arabic_text("نصيب العامل"),
                    self.format_arabic_text("التاريخ")
                ]
                
                operations_data = [headers]
                
                for operation in operations:
                    row = [
                        self.format_arabic_text(operation[1]),  # اسم الزبون
                        self.format_arabic_text(operation[2]),  # اسم الخدمة
                        self.format_arabic_text(operation[3]),  # من نفذها
                        f"{operation[5]:.2f} ج.م",  # السعر
                        f"{operation[6]:.2f} ج.م",  # نصيب العامل
                        operation[8][:10]  # التاريخ
                    ]
                    operations_data.append(row)
                
                operations_table = Table(operations_data, colWidths=[80, 80, 70, 60, 80, 70])
                operations_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(operations_table)
            
            # بناء PDF
            doc.build(story)
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير PDF: {e}")
            return False
    
    def export_to_excel(self, operations, stats, filename, title="تقرير العمليات"):
        """تصدير التقرير إلى Excel"""
        try:
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "تقرير العمليات"
            
            # تنسيق العنوان
            title_font = Font(size=16, bold=True)
            header_font = Font(size=12, bold=True)
            
            # العنوان
            ws['A1'] = title
            ws['A1'].font = title_font
            ws.merge_cells('A1:F1')
            ws['A1'].alignment = Alignment(horizontal='center')
            
            # تاريخ التقرير
            ws['A3'] = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            
            # الإحصائيات
            ws['A5'] = "الإحصائيات"
            ws['A5'].font = header_font
            
            stats_data = [
                ["البيان", "القيمة"],
                ["إجمالي العمليات", stats['total_operations']],
                ["إجمالي الإيرادات", f"{stats['total_revenue']:.2f} ج.م"],
                ["أرباح العاملين", f"{stats['employee_earnings']:.2f} ج.م"],
                ["أرباح المالك", f"{stats['owner_earnings']:.2f} ج.م"]
            ]
            
            for i, row in enumerate(stats_data):
                for j, value in enumerate(row):
                    cell = ws.cell(row=6+i, column=1+j, value=value)
                    if i == 0:  # رأس الجدول
                        cell.font = header_font
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # تفاصيل العمليات
            if operations:
                start_row = 12
                ws[f'A{start_row}'] = "تفاصيل العمليات"
                ws[f'A{start_row}'].font = header_font
                
                # رؤوس الأعمدة
                headers = ["الزبون", "الخدمة", "نفذها", "السعر", "نصيب العامل", "التاريخ"]
                for j, header in enumerate(headers):
                    cell = ws.cell(row=start_row+2, column=1+j, value=header)
                    cell.font = header_font
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                
                # البيانات
                for i, operation in enumerate(operations):
                    row_data = [
                        operation[1],  # اسم الزبون
                        operation[2],  # اسم الخدمة
                        operation[3],  # من نفذها
                        f"{operation[5]:.2f}",  # السعر
                        f"{operation[6]:.2f}",  # نصيب العامل
                        operation[8][:10]  # التاريخ
                    ]
                    
                    for j, value in enumerate(row_data):
                        ws.cell(row=start_row+3+i, column=1+j, value=value)
            
            # تعديل عرض الأعمدة
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # حفظ الملف
            wb.save(filename)
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير Excel: {e}")
            return False
