#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنماط وتحسينات بطاقات العاملين
تصميمات متنوعة وألوان مختلفة لبطاقات العاملين
"""

import random
from modern_theme import modern_theme

class EmployeeCardStyles:
    """فئة أنماط بطاقات العاملين"""
    
    def __init__(self):
        self.setup_card_styles()
        self.setup_employee_avatars()
        self.setup_card_colors()
    
    def setup_card_styles(self):
        """إعداد أنماط البطاقات المختلفة"""
        self.card_styles = [
            "primary",
            "secondary", 
            "success",
            "info",
            "warning",
            "danger"
        ]
    
    def setup_employee_avatars(self):
        """إعداد أيقونات متنوعة للعاملين"""
        self.employee_avatars = [
            "👨‍🦲",  # رجل أصلع
            "👨‍🦱",  # رجل شعر مجعد
            "👨‍🦳",  # رجل شعر أبيض
            "👨‍🦰",  # رجل شعر أحمر
            "👨",     # رجل عادي
            "🧔",     # رجل بلحية
            "👨‍💼",  # رجل أعمال
            "👷‍♂️",  # عامل
            "🧑‍🔧",  # فني
            "👨‍🎨",  # فنان
        ]
    
    def setup_card_colors(self):
        """إعداد ألوان البطاقات"""
        self.card_colors = {
            "primary": {
                "bg": modern_theme.colors['primary'],
                "text": modern_theme.colors['text_light'],
                "accent": modern_theme.colors['accent']
            },
            "secondary": {
                "bg": modern_theme.colors['secondary'],
                "text": modern_theme.colors['text_light'],
                "accent": modern_theme.colors['success']
            },
            "success": {
                "bg": modern_theme.colors['success'],
                "text": modern_theme.colors['text_light'],
                "accent": modern_theme.colors['warning']
            },
            "info": {
                "bg": modern_theme.colors['info'],
                "text": modern_theme.colors['text_light'],
                "accent": modern_theme.colors['danger']
            },
            "warning": {
                "bg": modern_theme.colors['warning'],
                "text": modern_theme.colors['text_primary'],
                "accent": modern_theme.colors['primary']
            },
            "danger": {
                "bg": modern_theme.colors['danger'],
                "text": modern_theme.colors['text_light'],
                "accent": modern_theme.colors['info']
            }
        }
    
    def get_random_card_style(self):
        """الحصول على نمط بطاقة عشوائي"""
        return random.choice(self.card_styles)
    
    def get_random_avatar(self):
        """الحصول على أيقونة عشوائية"""
        return random.choice(self.employee_avatars)
    
    def get_card_style_for_employee(self, employee_name):
        """الحصول على نمط بطاقة محدد للعامل"""
        # استخدام hash للحصول على نمط ثابت لكل عامل
        name_hash = hash(employee_name) % len(self.card_styles)
        return self.card_styles[name_hash]
    
    def get_avatar_for_employee(self, employee_name):
        """الحصول على أيقونة محددة للعامل"""
        # استخدام hash للحصول على أيقونة ثابتة لكل عامل
        name_hash = hash(employee_name) % len(self.employee_avatars)
        return self.employee_avatars[name_hash]
    
    def get_employee_card_info(self, employee_name):
        """الحصول على معلومات البطاقة الكاملة للعامل"""
        return {
            "name": employee_name,
            "style": self.get_card_style_for_employee(employee_name),
            "avatar": self.get_avatar_for_employee(employee_name),
            "colors": self.card_colors[self.get_card_style_for_employee(employee_name)]
        }
    
    def get_employee_specialties(self):
        """الحصول على تخصصات العاملين"""
        return [
            "قص شعر كلاسيكي",
            "حلاقة ذقن احترافية", 
            "تصفيف شعر عصري",
            "قص شعر أطفال",
            "حلاقة تقليدية",
            "تسريح مناسبات",
            "صبغ شعر",
            "علاج شعر",
            "حلاقة عروس",
            "قص شعر نسائي"
        ]
    
    def get_random_specialty(self):
        """الحصول على تخصص عشوائي"""
        return random.choice(self.get_employee_specialties())
    
    def get_specialty_for_employee(self, employee_name):
        """الحصول على تخصص محدد للعامل"""
        specialties = self.get_employee_specialties()
        name_hash = hash(employee_name) % len(specialties)
        return specialties[name_hash]
    
    def get_employee_stats(self, employee_name):
        """الحصول على إحصائيات وهمية للعامل"""
        # إنشاء إحصائيات وهمية بناءً على اسم العامل
        name_hash = abs(hash(employee_name))
        
        return {
            "total_customers": (name_hash % 50) + 20,
            "rating": round(4.0 + (name_hash % 10) / 10, 1),
            "experience_years": (name_hash % 10) + 1,
            "completed_services": (name_hash % 200) + 50
        }
    
    def create_enhanced_card_data(self, employee_name):
        """إنشاء بيانات بطاقة محسنة للعامل"""
        card_info = self.get_employee_card_info(employee_name)
        specialty = self.get_specialty_for_employee(employee_name)
        stats = self.get_employee_stats(employee_name)
        
        return {
            **card_info,
            "specialty": specialty,
            "stats": stats,
            "badges": self.get_employee_badges(stats),
            "status": "متاح",  # يمكن تطويرها لاحقاً
            "last_service": "منذ ساعتين"  # يمكن تطويرها لاحقاً
        }
    
    def get_employee_badges(self, stats):
        """الحصول على شارات العامل بناءً على الإحصائيات"""
        badges = []
        
        if stats["rating"] >= 4.5:
            badges.append({"icon": "⭐", "text": "نجم الصالون"})
        
        if stats["experience_years"] >= 5:
            badges.append({"icon": "🏆", "text": "خبير"})
        
        if stats["total_customers"] >= 40:
            badges.append({"icon": "👥", "text": "محبوب"})
        
        if stats["completed_services"] >= 150:
            badges.append({"icon": "💎", "text": "محترف"})
        
        return badges
    
    def get_card_animation_style(self, employee_name):
        """الحصول على نمط الحركة للبطاقة"""
        animations = [
            "fade_in",
            "slide_up", 
            "slide_left",
            "zoom_in",
            "bounce"
        ]
        
        name_hash = hash(employee_name) % len(animations)
        return animations[name_hash]
    
    def get_hover_effects(self):
        """الحصول على تأثيرات التمرير"""
        return {
            "scale": 1.05,
            "shadow": "0 4px 8px rgba(0,0,0,0.2)",
            "border_glow": True,
            "text_highlight": True
        }
    
    def get_card_layout_options(self):
        """الحصول على خيارات تخطيط البطاقات"""
        return {
            "compact": {
                "width": 200,
                "height": 150,
                "padding": 10,
                "show_stats": False
            },
            "standard": {
                "width": 250,
                "height": 200,
                "padding": 15,
                "show_stats": True
            },
            "detailed": {
                "width": 300,
                "height": 250,
                "padding": 20,
                "show_stats": True,
                "show_badges": True
            }
        }

# إنشاء مثيل عام لأنماط البطاقات
employee_card_styles = EmployeeCardStyles()
