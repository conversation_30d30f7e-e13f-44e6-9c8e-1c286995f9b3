# معلومات إصدار برنامج إدارة الحلاقة

__version__ = "1.0.0"
__author__ = "Augment Agent"
__email__ = "<EMAIL>"
__description__ = "برنامج شامل لإدارة عمليات الحلاقة والزبائن والخدمات والتقارير"
__license__ = "MIT"
__copyright__ = "Copyright 2025 Augment Code"

# تاريخ الإصدارات
VERSION_HISTORY = {
    "1.0.0": {
        "date": "2025-01-22",
        "features": [
            "نظام تسجيل دخول للمدير والعاملين",
            "إدارة الزبائن مع إمكانية إضافة الصور",
            "إدارة الخدمات والأسعار",
            "تسجيل العمليات مع حساب الأرباح التلقائي",
            "نظام تقارير شامل مع تصدير PDF و Excel",
            "دعم اللغة العربية والفرنسية",
            "واجهة حديثة باستخدام ttkbootstrap",
            "قاعدة بيانات SQLite محلية",
            "نظام إعدادات قابل للتخصيص",
            "إمكانية بناء ملف EXE مستقل"
        ],
        "bug_fixes": [],
        "notes": "الإصدار الأول من البرنامج"
    }
}

# متطلبات النظام
SYSTEM_REQUIREMENTS = {
    "os": "Windows 7 أو أحدث",
    "python": "3.8+",
    "ram": "512 MB",
    "storage": "100 MB",
    "dependencies": [
        "ttkbootstrap>=1.10.1",
        "Pillow>=10.0.0",
        "reportlab>=4.0.4",
        "openpyxl>=3.1.2",
        "python-bidi>=0.4.2",
        "arabic-reshaper>=3.0.0"
    ]
}

# معلومات الدعم
SUPPORT_INFO = {
    "documentation": "README.md",
    "issues": "يرجى الإبلاغ عن المشاكل عبر البريد الإلكتروني",
    "updates": "التحديثات متوفرة عند الطلب",
    "training": "دورات تدريبية متوفرة عند الحاجة"
}

def get_version_info():
    """الحصول على معلومات الإصدار"""
    return {
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "license": __license__,
        "copyright": __copyright__
    }

def get_latest_version():
    """الحصول على أحدث إصدار"""
    return max(VERSION_HISTORY.keys())

def get_version_features(version):
    """الحصول على مميزات إصدار معين"""
    return VERSION_HISTORY.get(version, {}).get("features", [])

def print_version_info():
    """طباعة معلومات الإصدار"""
    print(f"برنامج إدارة الحلاقة - الإصدار {__version__}")
    print(f"المطور: {__author__}")
    print(f"الوصف: {__description__}")
    print(f"الترخيص: {__license__}")
    print(f"حقوق النشر: {__copyright__}")

if __name__ == "__main__":
    print_version_info()
