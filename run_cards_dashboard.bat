@echo off
chcp 65001 >nul
title 🃏 برنامج إدارة الحلاقة - نظام البطاقات
color 0B

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              💈 برنامج إدارة الحلاقة 💈                    ██
echo ██                    نظام البطاقات السهل                    ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    pause
    exit /b 1
)

echo 🃏 تشغيل البرنامج مع نظام البطاقات الجديد...
echo.
echo ✨ مميزات نظام البطاقات:
echo    🎯 تنظيم مثالي للمعلومات
echo    🎨 تصميم عصري وجميل
echo    ⚡ وصول سريع للوظائف
echo    📱 واجهة بديهية وسهلة
echo.
echo 👨‍💼 بطاقات المدير:
echo    📊 بطاقة الإحصائيات العامة
echo       • 👥 الزبائن المسجلين
echo       • ✂️ الخدمات المتاحة
echo       • 💺 العمليات المكتملة
echo       • 👷‍♂️ العاملون النشطون
echo.
echo    🕐 بطاقة الأنشطة الحديثة
echo       • آخر 3 عمليات في النظام
echo       • تفاصيل كاملة لكل عملية
echo       • زر "عرض المزيد"
echo.
echo    ⚡ بطاقة الإجراءات السريعة
echo       • 👤 إضافة زبون
echo       • 💺 عملية جديدة
echo       • ✂️ إدارة الخدمات
echo       • 📊 التقارير
echo.
echo    🧭 بطاقة التنقل السريع
echo       • 👥 الزبائن
echo       • ✂️ الخدمات
echo       • 💺 العمليات
echo       • 📊 التقارير
echo.
echo    💈 بطاقة معلومات الصالون
echo       • اسم الصالون والعنوان
echo       • ساعات العمل والمدير
echo       • معلومات الاتصال
echo.
echo 👷‍♂️ بطاقات العامل:
echo    📊 بطاقة إحصائياتك الشخصية
echo       • 💺 إجمالي عملياتك
echo       • 💰 إجمالي أرباحك
echo       • 📅 عمليات اليوم
echo       • 🏆 أرباح اليوم
echo.
echo    💺 بطاقة عملياتك الحديثة
echo       • آخر 3 عمليات شخصية
echo       • تفاصيل كل عملية
echo       • زر "عرض المزيد"
echo.
echo    ⚡ بطاقة إجراءاتك السريعة
echo       • 💺 عملية جديدة
echo       • 👤 إضافة زبون
echo       • 📊 عملياتي
echo       • 🏆 إنجازاتي
echo.
echo    🏆 بطاقة إنجازاتك
echo       • أول 3 إنجازات محققة
echo       • تفاصيل كل إنجاز
echo       • زر "عرض جميع الإنجازات"
echo.
echo    💡 بطاقة نصائح وإرشادات
echo       • أول 3 نصائح مهنية
echo       • رسالة تحفيزية
echo       • إرشادات للتطوير
echo.
echo 🎨 المميزات البصرية:
echo    🌈 ألوان متناسقة حسب الدور
echo    🃏 بطاقات منظمة وجميلة
echo    📱 تخطيط متجاوب وسهل
echo    🎭 تأثيرات بصرية رائعة
echo.
echo 🔐 طريقة الدخول:
echo    👨‍💼 المدير: admin / admin123
echo    👷‍♂️ العاملون: اضغط على البطاقة
echo.
echo 💡 نصائح الاستخدام:
echo    • كل معلومة في بطاقة منفصلة
echo    • استخدم الأيقونات للتعرف السريع
echo    • اضغط على الأزرار للوصول المباشر
echo    • راجع البطاقات يومياً
echo    • استفد من التنظيم الجديد
echo.
echo 🌟 الفوائد:
echo    ⚡ وصول أسرع للمعلومات
echo    🎯 تنظيم أفضل للبيانات
echo    😊 تجربة استخدام ممتعة
echo    💪 كفاءة أعلى في العمل
echo.
echo ═══════════════════════════════════════
echo.

REM تشغيل البرنامج
python clean_run.py

REM في حالة إغلاق البرنامج
echo.
echo 🃏 شكراً لاستخدام نظام البطاقات!
echo 💈 نأمل أن تكون قد استمتعت بالتصميم الجديد!
echo 🌟 البساطة والجمال في خدمتك!
echo.
pause
