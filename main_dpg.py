#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة الحلاقة - Dear PyGui
نظام إدارة شامل لصالونات الحلاقة مع واجهة عصرية
"""

import dearpygui.dearpygui as dpg
import sqlite3
import datetime
import json
import os
from typing import Dict, List, Tuple, Optional

class CoiffeurApp:
    def __init__(self):
        """تهيئة التطبيق"""
        self.db_path = "coiffeur.db"
        self.current_user = None
        self.current_role = None
        self.language = "ar"
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # إعداد Dear PyGui
        self.setup_dpg()
        
        # إعداد الألوان والثيمات
        self.setup_theme()
        
        # إنشاء الواجهة
        self.create_interface()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT,
                role TEXT DEFAULT 'employee',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الزبائن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الخدمات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS services (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                duration INTEGER DEFAULT 30,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العمليات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS operations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT NOT NULL,
                service_name TEXT NOT NULL,
                price REAL NOT NULL,
                employee TEXT NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إضافة المدير الافتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, role) 
            VALUES ('admin', 'admin123', 'admin')
        ''')
        
        # إضافة خدمات افتراضية
        default_services = [
            ('حلاقة عادية', 25.0, 30, 'حلاقة تقليدية بالمقص والماكينة'),
            ('حلاقة وتشذيب لحية', 35.0, 45, 'حلاقة مع تشذيب وتنظيف اللحية'),
            ('صبغة شعر', 50.0, 60, 'صبغة الشعر بألوان مختلفة'),
            ('غسيل وتصفيف', 20.0, 25, 'غسيل الشعر وتصفيفه'),
            ('حلاقة VIP', 75.0, 90, 'حلاقة فاخرة مع خدمات إضافية')
        ]
        
        for service in default_services:
            cursor.execute('''
                INSERT OR IGNORE INTO services (name, price, duration, description) 
                VALUES (?, ?, ?, ?)
            ''', service)
        
        # إضافة عاملين افتراضيين
        default_employees = [
            ('أحمد محمد', None, 'employee'),
            ('محمود علي', None, 'employee'),
            ('خالد حسن', None, 'employee'),
            ('عمر يوسف', None, 'employee')
        ]
        
        for employee in default_employees:
            cursor.execute('''
                INSERT OR IGNORE INTO users (username, password, role) 
                VALUES (?, ?, ?)
            ''', employee)
        
        conn.commit()
        conn.close()

    def setup_dpg(self):
        """إعداد Dear PyGui"""
        dpg.create_context()
        dpg.create_viewport(
            title="💈 برنامج إدارة الحلاقة - Dear PyGui",
            width=1400,
            height=900,
            min_width=1200,
            min_height=700
        )
        
        # إعداد الخطوط الافتراضية
        # سيتم استخدام الخط الافتراضي للنظام

    def setup_theme(self):
        """إعداد الألوان والثيمات"""
        # ثيم داكن عصري
        with dpg.theme() as self.main_theme:
            with dpg.theme_component(dpg.mvAll):
                # ألوان الخلفية
                dpg.add_theme_color(dpg.mvThemeCol_WindowBg, (25, 25, 35, 255))
                dpg.add_theme_color(dpg.mvThemeCol_ChildBg, (35, 35, 45, 255))
                dpg.add_theme_color(dpg.mvThemeCol_PopupBg, (45, 45, 55, 255))
                
                # ألوان النصوص
                dpg.add_theme_color(dpg.mvThemeCol_Text, (255, 255, 255, 255))
                dpg.add_theme_color(dpg.mvThemeCol_TextDisabled, (128, 128, 128, 255))
                
                # ألوان الأزرار
                dpg.add_theme_color(dpg.mvThemeCol_Button, (70, 130, 200, 255))
                dpg.add_theme_color(dpg.mvThemeCol_ButtonHovered, (90, 150, 220, 255))
                dpg.add_theme_color(dpg.mvThemeCol_ButtonActive, (50, 110, 180, 255))
                
                # ألوان الإطارات
                dpg.add_theme_color(dpg.mvThemeCol_FrameBg, (55, 55, 65, 255))
                dpg.add_theme_color(dpg.mvThemeCol_FrameBgHovered, (65, 65, 75, 255))
                dpg.add_theme_color(dpg.mvThemeCol_FrameBgActive, (75, 75, 85, 255))
                
                # ألوان الحدود
                dpg.add_theme_color(dpg.mvThemeCol_Border, (100, 100, 110, 255))
                dpg.add_theme_color(dpg.mvThemeCol_BorderShadow, (0, 0, 0, 0))
                
                # تنسيق الحواف
                dpg.add_theme_style(dpg.mvStyleVar_FrameRounding, 8)
                dpg.add_theme_style(dpg.mvStyleVar_WindowRounding, 12)
                dpg.add_theme_style(dpg.mvStyleVar_ChildRounding, 8)
                dpg.add_theme_style(dpg.mvStyleVar_PopupRounding, 8)
                dpg.add_theme_style(dpg.mvStyleVar_ScrollbarRounding, 8)
                dpg.add_theme_style(dpg.mvStyleVar_GrabRounding, 8)
                dpg.add_theme_style(dpg.mvStyleVar_TabRounding, 8)
                
                # المسافات
                dpg.add_theme_style(dpg.mvStyleVar_WindowPadding, 15, 15)
                dpg.add_theme_style(dpg.mvStyleVar_FramePadding, 10, 8)
                dpg.add_theme_style(dpg.mvStyleVar_ItemSpacing, 12, 8)
                dpg.add_theme_style(dpg.mvStyleVar_ItemInnerSpacing, 8, 6)
        
        # ثيمات ملونة للبطاقات
        self.create_card_themes()
        
        dpg.bind_theme(self.main_theme)

    def create_card_themes(self):
        """إنشاء ثيمات البطاقات الملونة"""
        # بطاقة زرقاء (إحصائيات)
        with dpg.theme() as self.blue_card_theme:
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_ChildBg, (30, 80, 150, 100))
                dpg.add_theme_color(dpg.mvThemeCol_Border, (50, 120, 200, 255))
                dpg.add_theme_style(dpg.mvStyleVar_ChildRounding, 12)
                dpg.add_theme_style(dpg.mvStyleVar_ChildBorderSize, 2)
        
        # بطاقة خضراء (نجاح)
        with dpg.theme() as self.green_card_theme:
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_ChildBg, (30, 150, 80, 100))
                dpg.add_theme_color(dpg.mvThemeCol_Border, (50, 200, 120, 255))
                dpg.add_theme_style(dpg.mvStyleVar_ChildRounding, 12)
                dpg.add_theme_style(dpg.mvStyleVar_ChildBorderSize, 2)
        
        # بطاقة برتقالية (تحذير)
        with dpg.theme() as self.orange_card_theme:
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_ChildBg, (200, 120, 30, 100))
                dpg.add_theme_color(dpg.mvThemeCol_Border, (255, 160, 50, 255))
                dpg.add_theme_style(dpg.mvStyleVar_ChildRounding, 12)
                dpg.add_theme_style(dpg.mvStyleVar_ChildBorderSize, 2)
        
        # بطاقة حمراء (خطر)
        with dpg.theme() as self.red_card_theme:
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_ChildBg, (180, 50, 50, 100))
                dpg.add_theme_color(dpg.mvThemeCol_Border, (220, 80, 80, 255))
                dpg.add_theme_style(dpg.mvStyleVar_ChildRounding, 12)
                dpg.add_theme_style(dpg.mvStyleVar_ChildBorderSize, 2)

    def create_interface(self):
        """إنشاء الواجهة الرئيسية"""
        with dpg.window(label="الرئيسية", tag="main_window"):
            self.create_login_screen()
        
        dpg.set_primary_window("main_window", True)

    def create_login_screen(self):
        """إنشاء شاشة تسجيل الدخول"""
        # مسح المحتوى الحالي
        dpg.delete_item("main_window", children_only=True)
        
        # خلفية تدرجية
        with dpg.child_window(width=-1, height=-1, border=False):
            # عنوان رئيسي
            dpg.add_text("💈 برنامج إدارة الحلاقة", tag="main_title")
            dpg.add_text("نظام إدارة شامل وعصري", tag="subtitle")
            
            dpg.add_separator()
            dpg.add_spacing(count=3)
            
            # قسم تسجيل دخول المدير
            with dpg.group(horizontal=False):
                dpg.add_text("👨‍💼 تسجيل دخول المدير")
                
                with dpg.group(horizontal=True):
                    dpg.add_text("اسم المستخدم:")
                    dpg.add_input_text(tag="admin_username", default_value="admin", width=200)
                
                with dpg.group(horizontal=True):
                    dpg.add_text("كلمة المرور:")
                    dpg.add_input_text(tag="admin_password", default_value="admin123", 
                                     password=True, width=200)
                
                dpg.add_button(label="🚪 دخول كمدير", callback=self.admin_login, width=200)
            
            dpg.add_separator()
            dpg.add_spacing(count=3)
            
            # قسم العاملين
            dpg.add_text("👷‍♂️ العاملون")
            self.create_employee_cards()
            
            dpg.add_separator()
            dpg.add_spacing(count=2)
            
            # أزرار إضافية
            with dpg.group(horizontal=True):
                dpg.add_button(label="🌐 تغيير اللغة", callback=self.change_language)
                dpg.add_button(label="ℹ️ حول البرنامج", callback=self.show_about)

    def create_employee_cards(self):
        """إنشاء بطاقات العاملين"""
        employees = self.get_employees()
        
        with dpg.group(horizontal=True):
            for i, employee in enumerate(employees[:4]):  # أول 4 عاملين
                with dpg.child_window(width=250, height=150, tag=f"employee_card_{i}"):
                    dpg.bind_item_theme(dpg.last_item(), self.blue_card_theme)
                    
                    # أيقونة العامل
                    dpg.add_text("👷‍♂️", tag=f"employee_icon_{i}")
                    
                    # اسم العامل
                    dpg.add_text(employee[1], tag=f"employee_name_{i}")
                    
                    # معلومات إضافية
                    operations_count = self.get_employee_operations_count(employee[1])
                    dpg.add_text(f"العمليات: {operations_count}")
                    
                    # زر تسجيل الدخول
                    dpg.add_button(
                        label="🚪 دخول",
                        callback=lambda s, a, u=employee[1]: self.employee_login(u),
                        width=-1
                    )

    def get_employees(self) -> List[Tuple]:
        """الحصول على قائمة العاملين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE role = 'employee'")
        employees = cursor.fetchall()
        conn.close()
        return employees

    def get_employee_operations_count(self, employee_name: str) -> int:
        """الحصول على عدد عمليات العامل"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM operations WHERE employee = ?", (employee_name,))
        count = cursor.fetchone()[0]
        conn.close()
        return count

    def admin_login(self):
        """تسجيل دخول المدير"""
        username = dpg.get_value("admin_username")
        password = dpg.get_value("admin_password")
        
        if self.verify_admin_login(username, password):
            self.current_user = username
            self.current_role = "admin"
            self.create_dashboard()
        else:
            self.show_error("خطأ في تسجيل الدخول", "اسم المستخدم أو كلمة المرور غير صحيحة")

    def employee_login(self, username: str):
        """تسجيل دخول العامل"""
        self.current_user = username
        self.current_role = "employee"
        self.create_dashboard()

    def verify_admin_login(self, username: str, password: str) -> bool:
        """التحقق من بيانات المدير"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "SELECT * FROM users WHERE username = ? AND password = ? AND role = 'admin'",
            (username, password)
        )
        result = cursor.fetchone()
        conn.close()
        return result is not None

    def create_dashboard(self):
        """إنشاء لوحة التحكم"""
        # مسح المحتوى الحالي
        dpg.delete_item("main_window", children_only=True)
        
        # رأس لوحة التحكم
        self.create_dashboard_header()
        
        # محتوى لوحة التحكم
        if self.current_role == "admin":
            self.create_admin_dashboard()
        else:
            self.create_employee_dashboard()

    def create_dashboard_header(self):
        """إنشاء رأس لوحة التحكم"""
        with dpg.child_window(height=80, border=False):
            with dpg.group(horizontal=True):
                # العنوان والترحيب
                with dpg.group():
                    role_icon = "👨‍💼" if self.current_role == "admin" else "👷‍♂️"
                    role_text = "المدير" if self.current_role == "admin" else "العامل"
                    dpg.add_text(f"{role_icon} لوحة تحكم {role_text}")
                    dpg.add_text(f"مرحباً {self.current_user} 👋")
                
                # أزرار التحكم
                with dpg.group(horizontal=True):
                    dpg.add_button(label="🌐 اللغة", callback=self.change_language)
                    dpg.add_button(label="🚪 خروج", callback=self.logout)
                    
                    # الوقت الحالي
                    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
                    dpg.add_text(f"🕐 {current_time}")

    def create_admin_dashboard(self):
        """إنشاء لوحة تحكم المدير"""
        with dpg.child_window(width=-1, height=-1, border=False):
            # الصف الأول - الإحصائيات
            with dpg.group(horizontal=True):
                self.create_stats_card("📊 الإحصائيات العامة", self.get_admin_stats(), self.blue_card_theme)
                self.create_activities_card("🕐 الأنشطة الحديثة", self.get_recent_activities(), self.orange_card_theme)
            
            dpg.add_spacing(count=2)
            
            # الصف الثاني - الإجراءات
            with dpg.group(horizontal=True):
                self.create_actions_card("⚡ الإجراءات السريعة", self.get_admin_actions(), self.green_card_theme)
                self.create_navigation_card("🧭 التنقل السريع", self.get_admin_navigation(), self.red_card_theme)

    def create_employee_dashboard(self):
        """إنشاء لوحة تحكم العامل"""
        with dpg.child_window(width=-1, height=-1, border=False):
            # الصف الأول - الإحصائيات الشخصية
            with dpg.group(horizontal=True):
                self.create_stats_card("📊 إحصائياتك الشخصية", self.get_employee_stats(), self.blue_card_theme)
                self.create_activities_card("💺 عملياتك الحديثة", self.get_employee_activities(), self.green_card_theme)
            
            dpg.add_spacing(count=2)
            
            # الصف الثاني - الإجراءات والإنجازات
            with dpg.group(horizontal=True):
                self.create_actions_card("⚡ إجراءاتك السريعة", self.get_employee_actions(), self.orange_card_theme)
                self.create_achievements_card("🏆 إنجازاتك", self.get_employee_achievements(), self.red_card_theme)

    def create_stats_card(self, title: str, stats_data: List, theme):
        """إنشاء بطاقة الإحصائيات"""
        with dpg.child_window(width=680, height=300, border=True):
            dpg.bind_item_theme(dpg.last_item(), theme)

            dpg.add_text(title, tag=f"stats_title_{id(self)}")
            dpg.add_separator()

            # عرض الإحصائيات في شبكة 2x2
            with dpg.table(header_row=False, borders_innerH=True, borders_innerV=True):
                dpg.add_table_column()
                dpg.add_table_column()

                for i in range(0, len(stats_data), 2):
                    with dpg.table_row():
                        # العمود الأول
                        if i < len(stats_data):
                            stat = stats_data[i]
                            with dpg.group():
                                dpg.add_text(f"{stat['icon']} {stat['name']}")
                                dpg.add_text(f"{stat['value']}", color=(255, 255, 100))
                                dpg.add_text(stat['desc'], color=(200, 200, 200))

                        # العمود الثاني
                        if i + 1 < len(stats_data):
                            stat = stats_data[i + 1]
                            with dpg.group():
                                dpg.add_text(f"{stat['icon']} {stat['name']}")
                                dpg.add_text(f"{stat['value']}", color=(255, 255, 100))
                                dpg.add_text(stat['desc'], color=(200, 200, 200))

    def create_activities_card(self, title: str, activities_data: List, theme):
        """إنشاء بطاقة الأنشطة"""
        with dpg.child_window(width=680, height=300, border=True):
            dpg.bind_item_theme(dpg.last_item(), theme)

            dpg.add_text(title)
            dpg.add_separator()

            if activities_data:
                for activity in activities_data[:5]:  # أول 5 أنشطة
                    with dpg.group(horizontal=True):
                        dpg.add_text("✂️")
                        with dpg.group():
                            dpg.add_text(activity['main_text'])
                            dpg.add_text(activity['sub_text'], color=(200, 200, 200))
                        dpg.add_text(activity['time'], color=(150, 150, 150))
                    dpg.add_spacing()

                dpg.add_button(label="عرض المزيد", callback=self.show_operations)
            else:
                dpg.add_text("📝 لا توجد أنشطة حديثة", color=(150, 150, 150))

    def create_actions_card(self, title: str, actions_data: List, theme):
        """إنشاء بطاقة الإجراءات"""
        with dpg.child_window(width=680, height=300, border=True):
            dpg.bind_item_theme(dpg.last_item(), theme)

            dpg.add_text(title)
            dpg.add_separator()

            # عرض الإجراءات في شبكة 2x2
            with dpg.table(header_row=False):
                dpg.add_table_column()
                dpg.add_table_column()

                for i in range(0, len(actions_data), 2):
                    with dpg.table_row():
                        # العمود الأول
                        if i < len(actions_data):
                            action = actions_data[i]
                            dpg.add_button(
                                label=f"{action['icon']} {action['name']}",
                                callback=action['callback'],
                                width=300,
                                height=40
                            )

                        # العمود الثاني
                        if i + 1 < len(actions_data):
                            action = actions_data[i + 1]
                            dpg.add_button(
                                label=f"{action['icon']} {action['name']}",
                                callback=action['callback'],
                                width=300,
                                height=40
                            )

    def create_navigation_card(self, title: str, nav_data: List, theme):
        """إنشاء بطاقة التنقل"""
        with dpg.child_window(width=680, height=300, border=True):
            dpg.bind_item_theme(dpg.last_item(), theme)

            dpg.add_text(title)
            dpg.add_separator()

            for nav_item in nav_data:
                dpg.add_button(
                    label=f"{nav_item['icon']} {nav_item['name']}",
                    callback=nav_item['callback'],
                    width=-1,
                    height=40
                )
                dpg.add_spacing()

    def create_achievements_card(self, title: str, achievements_data: List, theme):
        """إنشاء بطاقة الإنجازات"""
        with dpg.child_window(width=680, height=300, border=True):
            dpg.bind_item_theme(dpg.last_item(), theme)

            dpg.add_text(title)
            dpg.add_separator()

            if achievements_data:
                for achievement in achievements_data[:4]:  # أول 4 إنجازات
                    with dpg.group(horizontal=True):
                        dpg.add_text(achievement['icon'])
                        with dpg.group():
                            dpg.add_text(achievement['name'], color=(255, 215, 0))
                            dpg.add_text(achievement['desc'], color=(200, 200, 200))
                    dpg.add_spacing()

                dpg.add_button(label="عرض جميع الإنجازات", callback=self.show_achievements)
            else:
                dpg.add_text("🎯 ابدأ العمل لتحصل على إنجازات!", color=(150, 150, 150))

    # دوال جلب البيانات
    def get_admin_stats(self) -> List[Dict]:
        """الحصول على إحصائيات المدير"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # عدد الزبائن
        cursor.execute("SELECT COUNT(*) FROM customers")
        customers_count = cursor.fetchone()[0]

        # عدد الخدمات
        cursor.execute("SELECT COUNT(*) FROM services")
        services_count = cursor.fetchone()[0]

        # عدد العمليات
        cursor.execute("SELECT COUNT(*) FROM operations")
        operations_count = cursor.fetchone()[0]

        # عدد العاملين
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'employee'")
        employees_count = cursor.fetchone()[0]

        conn.close()

        return [
            {'icon': '👥', 'name': 'الزبائن', 'value': customers_count, 'desc': 'زبون مسجل'},
            {'icon': '✂️', 'name': 'الخدمات', 'value': services_count, 'desc': 'خدمة متاحة'},
            {'icon': '💺', 'name': 'العمليات', 'value': operations_count, 'desc': 'عملية مكتملة'},
            {'icon': '👷‍♂️', 'name': 'العاملون', 'value': employees_count, 'desc': 'عامل نشط'}
        ]

    def get_recent_activities(self) -> List[Dict]:
        """الحصول على الأنشطة الحديثة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM operations ORDER BY created_at DESC LIMIT 5")
        operations = cursor.fetchall()
        conn.close()

        activities = []
        for op in operations:
            activities.append({
                'main_text': f"عملية جديدة للزبون: {op[1]}",
                'sub_text': f"الخدمة: {op[2]} | السعر: {op[3]} ج.م",
                'time': op[6][:16] if len(op) > 6 else "غير محدد"
            })

        return activities

    def get_admin_actions(self) -> List[Dict]:
        """الحصول على إجراءات المدير"""
        return [
            {'icon': '👤', 'name': 'إضافة زبون', 'callback': self.show_add_customer},
            {'icon': '💺', 'name': 'عملية جديدة', 'callback': self.show_add_operation},
            {'icon': '✂️', 'name': 'إدارة الخدمات', 'callback': self.show_services},
            {'icon': '📊', 'name': 'التقارير', 'callback': self.show_reports}
        ]

    def get_admin_navigation(self) -> List[Dict]:
        """الحصول على تنقل المدير"""
        return [
            {'icon': '👥', 'name': 'الزبائن', 'callback': self.show_customers},
            {'icon': '✂️', 'name': 'الخدمات', 'callback': self.show_services},
            {'icon': '💺', 'name': 'العمليات', 'callback': self.show_operations},
            {'icon': '📊', 'name': 'التقارير', 'callback': self.show_reports}
        ]

    def get_employee_stats(self) -> List[Dict]:
        """الحصول على إحصائيات العامل"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # عمليات العامل
        cursor.execute("SELECT COUNT(*) FROM operations WHERE employee = ?", (self.current_user,))
        total_operations = cursor.fetchone()[0]

        # أرباح العامل
        cursor.execute("SELECT SUM(price) FROM operations WHERE employee = ?", (self.current_user,))
        total_earnings = cursor.fetchone()[0] or 0

        # عمليات اليوم
        today = datetime.date.today().strftime("%Y-%m-%d")
        cursor.execute("SELECT COUNT(*) FROM operations WHERE employee = ? AND DATE(created_at) = ?",
                      (self.current_user, today))
        today_operations = cursor.fetchone()[0]

        # أرباح اليوم
        cursor.execute("SELECT SUM(price) FROM operations WHERE employee = ? AND DATE(created_at) = ?",
                      (self.current_user, today))
        today_earnings = cursor.fetchone()[0] or 0

        conn.close()

        return [
            {'icon': '💺', 'name': 'إجمالي عملياتك', 'value': total_operations, 'desc': 'عملية مكتملة'},
            {'icon': '💰', 'name': 'إجمالي أرباحك', 'value': f"{total_earnings:.0f} ج.م", 'desc': 'الأرباح الكلية'},
            {'icon': '📅', 'name': 'عمليات اليوم', 'value': today_operations, 'desc': 'عمليات اليوم'},
            {'icon': '🏆', 'name': 'أرباح اليوم', 'value': f"{today_earnings:.0f} ج.م", 'desc': 'أرباح اليوم'}
        ]

    def get_employee_activities(self) -> List[Dict]:
        """الحصول على أنشطة العامل"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM operations WHERE employee = ? ORDER BY created_at DESC LIMIT 5",
                      (self.current_user,))
        operations = cursor.fetchall()
        conn.close()

        activities = []
        for op in operations:
            activities.append({
                'main_text': f"قمت بخدمة الزبون: {op[1]}",
                'sub_text': f"الخدمة: {op[2]} | ربحت: {op[3]} ج.م",
                'time': op[6][:16] if len(op) > 6 else "غير محدد"
            })

        return activities

    def get_employee_actions(self) -> List[Dict]:
        """الحصول على إجراءات العامل"""
        return [
            {'icon': '💺', 'name': 'عملية جديدة', 'callback': self.show_add_operation},
            {'icon': '👤', 'name': 'إضافة زبون', 'callback': self.show_add_customer},
            {'icon': '📊', 'name': 'عملياتي', 'callback': self.show_my_operations},
            {'icon': '🏆', 'name': 'إنجازاتي', 'callback': self.show_achievements}
        ]

    def get_employee_achievements(self) -> List[Dict]:
        """الحصول على إنجازات العامل"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # عدد العمليات
        cursor.execute("SELECT COUNT(*) FROM operations WHERE employee = ?", (self.current_user,))
        total_operations = cursor.fetchone()[0]

        # إجمالي الأرباح
        cursor.execute("SELECT SUM(price) FROM operations WHERE employee = ?", (self.current_user,))
        total_earnings = cursor.fetchone()[0] or 0

        conn.close()

        achievements = []

        if total_operations >= 1:
            achievements.append({'icon': '🎯', 'name': 'البداية', 'desc': 'أول عملية'})
        if total_operations >= 10:
            achievements.append({'icon': '⭐', 'name': 'نجم صاعد', 'desc': '10 عمليات'})
        if total_operations >= 25:
            achievements.append({'icon': '🏅', 'name': 'محترف', 'desc': '25 عملية'})
        if total_earnings >= 500:
            achievements.append({'icon': '💰', 'name': 'مربح', 'desc': '500 ج.م أرباح'})

        return achievements

    # دوال الواجهات الفرعية
    def show_add_customer(self):
        """عرض نافذة إضافة زبون"""
        with dpg.window(label="إضافة زبون جديد", modal=True, show=True, width=400, height=300):
            dpg.add_text("👤 إضافة زبون جديد")
            dpg.add_separator()

            dpg.add_input_text(label="الاسم", tag="customer_name")
            dpg.add_input_text(label="الهاتف", tag="customer_phone")
            dpg.add_input_text(label="البريد الإلكتروني", tag="customer_email")
            dpg.add_input_text(label="ملاحظات", tag="customer_notes", multiline=True)

            with dpg.group(horizontal=True):
                dpg.add_button(label="💾 حفظ", callback=self.save_customer)
                dpg.add_button(label="❌ إلغاء", callback=lambda: dpg.delete_item(dpg.last_container()))

    def show_add_operation(self):
        """عرض نافذة إضافة عملية"""
        with dpg.window(label="عملية جديدة", modal=True, show=True, width=400, height=350):
            dpg.add_text("💺 تسجيل عملية جديدة")
            dpg.add_separator()

            dpg.add_input_text(label="اسم الزبون", tag="operation_customer")

            # قائمة الخدمات
            services = self.get_services()
            service_names = [service[1] for service in services]
            dpg.add_combo(label="الخدمة", items=service_names, tag="operation_service")

            dpg.add_input_float(label="السعر", tag="operation_price", default_value=25.0)
            dpg.add_input_text(label="ملاحظات", tag="operation_notes", multiline=True)

            with dpg.group(horizontal=True):
                dpg.add_button(label="💾 حفظ", callback=self.save_operation)
                dpg.add_button(label="❌ إلغاء", callback=lambda: dpg.delete_item(dpg.last_container()))

    def get_services(self) -> List[Tuple]:
        """الحصول على قائمة الخدمات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM services")
        services = cursor.fetchall()
        conn.close()
        return services

    def save_customer(self):
        """حفظ زبون جديد"""
        name = dpg.get_value("customer_name")
        phone = dpg.get_value("customer_phone")
        email = dpg.get_value("customer_email")
        notes = dpg.get_value("customer_notes")

        if name:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO customers (name, phone, email, notes) VALUES (?, ?, ?, ?)",
                (name, phone, email, notes)
            )
            conn.commit()
            conn.close()

            self.show_success("تم الحفظ", "تم إضافة الزبون بنجاح")
            dpg.delete_item(dpg.last_container())
        else:
            self.show_error("خطأ", "يرجى إدخال اسم الزبون")

    def save_operation(self):
        """حفظ عملية جديدة"""
        customer = dpg.get_value("operation_customer")
        service = dpg.get_value("operation_service")
        price = dpg.get_value("operation_price")
        notes = dpg.get_value("operation_notes")

        if customer and service:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO operations (customer_name, service_name, price, employee, notes) VALUES (?, ?, ?, ?, ?)",
                (customer, service, price, self.current_user, notes)
            )
            conn.commit()
            conn.close()

            self.show_success("تم الحفظ", "تم تسجيل العملية بنجاح")
            dpg.delete_item(dpg.last_container())
        else:
            self.show_error("خطأ", "يرجى إدخال جميع البيانات المطلوبة")

    # دوال مساعدة
    def show_error(self, title: str, message: str):
        """عرض رسالة خطأ"""
        with dpg.window(label=title, modal=True, show=True, width=300, height=150):
            dpg.add_text(f"❌ {message}")
            dpg.add_button(label="موافق", callback=lambda: dpg.delete_item(dpg.last_container()))

    def show_success(self, title: str, message: str):
        """عرض رسالة نجاح"""
        with dpg.window(label=title, modal=True, show=True, width=300, height=150):
            dpg.add_text(f"✅ {message}")
            dpg.add_button(label="موافق", callback=lambda: dpg.delete_item(dpg.last_container()))

    def change_language(self):
        """تغيير اللغة"""
        self.language = "en" if self.language == "ar" else "ar"
        self.show_success("تم التغيير", f"تم تغيير اللغة إلى {self.language}")

    def show_about(self):
        """عرض معلومات البرنامج"""
        with dpg.window(label="حول البرنامج", modal=True, show=True, width=400, height=300):
            dpg.add_text("💈 برنامج إدارة الحلاقة")
            dpg.add_text("الإصدار: 2.0 - Dear PyGui")
            dpg.add_separator()
            dpg.add_text("نظام إدارة شامل وعصري لصالونات الحلاقة")
            dpg.add_text("يتميز بواجهة حديثة وسهولة في الاستخدام")
            dpg.add_separator()
            dpg.add_text("المطور: فريق التطوير")
            dpg.add_text("التاريخ: 2024")
            dpg.add_button(label="إغلاق", callback=lambda: dpg.delete_item(dpg.last_container()))

    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.current_role = None
        self.create_login_screen()

    # دوال فارغة للوظائف المستقبلية
    def show_customers(self): pass
    def show_services(self): pass
    def show_operations(self): pass
    def show_reports(self): pass
    def show_my_operations(self): pass
    def show_achievements(self): pass

    def run(self):
        """تشغيل التطبيق"""
        dpg.setup_dearpygui()
        dpg.show_viewport()
        dpg.start_dearpygui()
        dpg.destroy_context()

# تشغيل التطبيق
if __name__ == "__main__":
    app = CoiffeurApp()
    app.run()
