@echo off
title برنامج إدارة الحلاقة
echo ================================
echo    برنامج إدارة الحلاقة
echo ================================
echo.
echo تشغيل البرنامج...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من python.org
    pause
    exit /b 1
)

REM تشغيل البرنامج
python run.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo تحقق من تثبيت المكتبات المطلوبة:
    echo pip install -r requirements.txt
    echo.
    pause
)
