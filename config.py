# ملف إعدادات برنامج الحلاقة

import os
import json

class Config:
    """فئة إدارة إعدادات البرنامج"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.default_config = {
            "database": {
                "name": "coiffeur.db",
                "backup_enabled": True,
                "backup_interval": 24  # ساعات
            },
            "ui": {
                "theme": "cosmo",
                "language": "ar",
                "window_size": "1200x800",
                "font_size": 12
            },
            "business": {
                "currency": "ج.م",
                "employee_share_percentage": 50,
                "owner_share_percentage": 50,
                "tax_rate": 0.0
            },
            "reports": {
                "default_format": "pdf",
                "include_images": False,
                "auto_export": False
            },
            "security": {
                "session_timeout": 0,  # 0 = لا يوجد انتهاء صلاحية
                "require_admin_password": True,
                "auto_logout": False
            }
        }
        self.config = self.load_config()
    
    def load_config(self):
        """تحميل الإعدادات من الملف"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # دمج الإعدادات الافتراضية مع المحملة
                return self.merge_configs(self.default_config, config)
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {e}")
                return self.default_config.copy()
        else:
            # إنشاء ملف إعدادات جديد
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def merge_configs(self, default, loaded):
        """دمج الإعدادات الافتراضية مع المحملة"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def save_config(self, config=None):
        """حفظ الإعدادات إلى الملف"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get(self, section, key, default=None):
        """الحصول على قيمة إعداد معين"""
        return self.config.get(section, {}).get(key, default)
    
    def set(self, section, key, value):
        """تعيين قيمة إعداد معين"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
        self.save_config()
    
    def get_database_path(self):
        """الحصول على مسار قاعدة البيانات"""
        return self.get('database', 'name', 'coiffeur.db')
    
    def get_theme(self):
        """الحصول على ثيم الواجهة"""
        return self.get('ui', 'theme', 'cosmo')
    
    def get_language(self):
        """الحصول على اللغة الافتراضية"""
        return self.get('ui', 'language', 'ar')
    
    def get_window_size(self):
        """الحصول على حجم النافذة"""
        return self.get('ui', 'window_size', '1200x800')
    
    def get_currency(self):
        """الحصول على رمز العملة"""
        return self.get('business', 'currency', 'ج.م')
    
    def get_employee_share_percentage(self):
        """الحصول على نسبة العامل"""
        return self.get('business', 'employee_share_percentage', 50)
    
    def get_owner_share_percentage(self):
        """الحصول على نسبة المالك"""
        return self.get('business', 'owner_share_percentage', 50)
    
    def is_backup_enabled(self):
        """التحقق من تفعيل النسخ الاحتياطي"""
        return self.get('database', 'backup_enabled', True)
    
    def get_backup_interval(self):
        """الحصول على فترة النسخ الاحتياطي بالساعات"""
        return self.get('database', 'backup_interval', 24)
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        self.config = self.default_config.copy()
        self.save_config()
    
    def export_config(self, filename):
        """تصدير الإعدادات إلى ملف"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {e}")
            return False
    
    def import_config(self, filename):
        """استيراد الإعدادات من ملف"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            self.config = self.merge_configs(self.default_config, imported_config)
            self.save_config()
            return True
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {e}")
            return False

# إنشاء مثيل عام للإعدادات
app_config = Config()
