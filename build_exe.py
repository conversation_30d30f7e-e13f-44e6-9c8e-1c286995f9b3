#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لبناء ملف EXE من برنامج الحلاقة
يستخدم PyInstaller لإنشاء ملف تنفيذي مستقل
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """تثبيت PyInstaller إذا لم يكن مثبتاً"""
    try:
        import PyInstaller
        print("PyInstaller متوفر بالفعل")
        return True
    except ImportError:
        print("تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            return True
        except subprocess.CalledProcessError:
            print("فشل في تثبيت PyInstaller")
            return False

def create_spec_file():
    """إنشاء ملف spec مخصص للبرنامج"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('*.py', '.'),
        ('*.db', '.'),
        ('images', 'images'),
    ],
    hiddenimports=[
        'ttkbootstrap',
        'PIL',
        'reportlab',
        'openpyxl',
        'arabic_reshaper',
        'bidi',
        'sqlite3',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CoiffeurApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('coiffeur.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("تم إنشاء ملف coiffeur.spec")

def build_exe():
    """بناء ملف EXE"""
    print("بدء عملية البناء...")
    
    try:
        # تنظيف المجلدات السابقة
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # بناء EXE
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "coiffeur.spec"]
        subprocess.check_call(cmd)
        
        print("تم بناء البرنامج بنجاح!")
        print("الملف التنفيذي متوفر في مجلد dist/")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"فشل في بناء البرنامج: {e}")
        return False

def create_installer_script():
    """إنشاء سكريبت تثبيت بسيط"""
    installer_content = '''@echo off
echo تثبيت برنامج إدارة الحلاقة
echo ========================

if not exist "CoiffeurApp" mkdir "CoiffeurApp"

echo نسخ الملفات...
copy "CoiffeurApp.exe" "CoiffeurApp\\"
copy "*.dll" "CoiffeurApp\\" 2>nul

echo إنشاء اختصار على سطح المكتب...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\برنامج الحلاقة.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CD%\\CoiffeurApp\\CoiffeurApp.exe" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo تم التثبيت بنجاح!
echo يمكنك الآن تشغيل البرنامج من سطح المكتب
pause
'''
    
    with open('dist/install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("تم إنشاء سكريبت التثبيت: dist/install.bat")

def main():
    """الدالة الرئيسية"""
    print("=== بناء برنامج إدارة الحلاقة ===")
    print()
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ['run.py', 'main.py', 'database.py', 'translations.py', 'reports.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("الملفات التالية مفقودة:")
        for file in missing_files:
            print(f"- {file}")
        return
    
    # تثبيت PyInstaller
    if not install_pyinstaller():
        return
    
    # إنشاء ملف spec
    create_spec_file()
    
    # بناء EXE
    if build_exe():
        create_installer_script()
        print()
        print("تم الانتهاء من البناء!")
        print("الملفات متوفرة في مجلد dist/")
        print("يمكنك تشغيل install.bat لتثبيت البرنامج")
    else:
        print("فشل في بناء البرنامج")

if __name__ == "__main__":
    main()
