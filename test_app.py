#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات برنامج إدارة الحلاقة
للتأكد من عمل جميع المكونات بشكل صحيح
"""

import unittest
import os
import tempfile
import shutil
from database import Database
from translations import Translator
from config import Config

class TestDatabase(unittest.TestCase):
    """اختبارات قاعدة البيانات"""
    
    def setUp(self):
        """إعداد قاعدة بيانات مؤقتة للاختبار"""
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db = Database(self.test_db_path)
    
    def tearDown(self):
        """تنظيف قاعدة البيانات المؤقتة"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def test_database_creation(self):
        """اختبار إنشاء قاعدة البيانات"""
        self.assertTrue(os.path.exists(self.test_db_path))
    
    def test_admin_user_exists(self):
        """اختبار وجود المدير الافتراضي"""
        result = self.db.authenticate_user('admin', 'admin123')
        self.assertIsNotNone(result)
        self.assertEqual(result[1], 'admin')
    
    def test_add_user(self):
        """اختبار إضافة مستخدم جديد"""
        success = self.db.add_user('test_employee', role='employee')
        self.assertTrue(success)
        
        # التحقق من إمكانية تسجيل الدخول
        result = self.db.authenticate_user('test_employee')
        self.assertIsNotNone(result)
        self.assertEqual(result[1], 'employee')
    
    def test_add_customer(self):
        """اختبار إضافة زبون"""
        customer_id = self.db.add_customer('أحمد محمد', '01234567890', None, 'admin')
        self.assertIsNotNone(customer_id)
        self.assertGreater(customer_id, 0)
        
        # التحقق من وجود الزبون
        customers = self.db.get_customers()
        self.assertEqual(len(customers), 1)
        self.assertEqual(customers[0][1], 'أحمد محمد')
    
    def test_add_service(self):
        """اختبار إضافة خدمة"""
        self.db.add_service('قص شعر اختبار', 25.0)
        
        services = self.db.get_services()
        # يجب أن تكون هناك الخدمات الافتراضية + الخدمة الجديدة
        self.assertGreater(len(services), 5)
        
        # البحث عن الخدمة الجديدة
        test_service = next((s for s in services if s[1] == 'قص شعر اختبار'), None)
        self.assertIsNotNone(test_service)
        self.assertEqual(test_service[2], 25.0)
    
    def test_add_operation(self):
        """اختبار إضافة عملية"""
        # إضافة زبون وخدمة أولاً
        customer_id = self.db.add_customer('زبون اختبار', '***********', None, 'admin')
        self.db.add_service('خدمة اختبار', 50.0)
        
        # الحصول على معرف الخدمة
        services = self.db.get_services()
        service_id = next(s[0] for s in services if s[1] == 'خدمة اختبار')
        
        # إضافة العملية
        self.db.add_operation(customer_id, service_id, 'admin', 'admin', 50.0)
        
        # التحقق من العملية
        operations = self.db.get_operations()
        self.assertEqual(len(operations), 1)
        self.assertEqual(operations[0][5], 50.0)  # السعر
        self.assertEqual(operations[0][6], 50.0)  # نصيب العامل (100% لأنه صاحب الزبون)

class TestTranslations(unittest.TestCase):
    """اختبارات الترجمات"""
    
    def test_arabic_translation(self):
        """اختبار الترجمة العربية"""
        translator = Translator('ar')
        self.assertEqual(translator.get('login'), 'دخول')
        self.assertEqual(translator.get('customers'), 'الزبائن')
    
    def test_french_translation(self):
        """اختبار الترجمة الفرنسية"""
        translator = Translator('fr')
        self.assertEqual(translator.get('login'), 'Connexion')
        self.assertEqual(translator.get('customers'), 'Clients')
    
    def test_language_switching(self):
        """اختبار تبديل اللغة"""
        translator = Translator('ar')
        self.assertEqual(translator.get('login'), 'دخول')
        
        translator.set_language('fr')
        self.assertEqual(translator.get('login'), 'Connexion')
    
    def test_missing_translation(self):
        """اختبار النص المفقود"""
        translator = Translator('ar')
        # يجب أن يعيد المفتاح نفسه إذا لم توجد الترجمة
        self.assertEqual(translator.get('nonexistent_key'), 'nonexistent_key')

class TestConfig(unittest.TestCase):
    """اختبارات الإعدادات"""
    
    def setUp(self):
        """إعداد ملف إعدادات مؤقت"""
        self.test_config_path = tempfile.mktemp(suffix='.json')
        self.config = Config(self.test_config_path)
    
    def tearDown(self):
        """تنظيف ملف الإعدادات المؤقت"""
        if os.path.exists(self.test_config_path):
            os.remove(self.test_config_path)
    
    def test_default_config(self):
        """اختبار الإعدادات الافتراضية"""
        self.assertEqual(self.config.get_language(), 'ar')
        self.assertEqual(self.config.get_theme(), 'cosmo')
        self.assertEqual(self.config.get_currency(), 'ج.م')
    
    def test_config_persistence(self):
        """اختبار حفظ وتحميل الإعدادات"""
        # تغيير إعداد
        self.config.set('ui', 'language', 'fr')
        
        # إنشاء مثيل جديد لتحميل الإعدادات
        new_config = Config(self.test_config_path)
        self.assertEqual(new_config.get_language(), 'fr')
    
    def test_config_export_import(self):
        """اختبار تصدير واستيراد الإعدادات"""
        # تغيير بعض الإعدادات
        self.config.set('ui', 'language', 'fr')
        self.config.set('ui', 'theme', 'darkly')
        
        # تصدير الإعدادات
        export_path = tempfile.mktemp(suffix='.json')
        success = self.config.export_config(export_path)
        self.assertTrue(success)
        
        # إنشاء إعدادات جديدة واستيراد
        new_config = Config()
        success = new_config.import_config(export_path)
        self.assertTrue(success)
        self.assertEqual(new_config.get_language(), 'fr')
        self.assertEqual(new_config.get_theme(), 'darkly')
        
        # تنظيف
        os.remove(export_path)

class TestBusinessLogic(unittest.TestCase):
    """اختبارات منطق العمل"""
    
    def setUp(self):
        """إعداد قاعدة بيانات مؤقتة"""
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db = Database(self.test_db_path)
    
    def tearDown(self):
        """تنظيف"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def test_profit_calculation_same_owner(self):
        """اختبار حساب الأرباح عندما يكون العامل هو صاحب الزبون"""
        # إضافة البيانات المطلوبة
        customer_id = self.db.add_customer('زبون', '***********', None, 'employee1')
        self.db.add_service('خدمة', 100.0)
        
        services = self.db.get_services()
        service_id = next(s[0] for s in services if s[1] == 'خدمة')
        
        # العامل ينفذ خدمة لزبونه
        self.db.add_operation(customer_id, service_id, 'employee1', 'employee1', 100.0)
        
        operations = self.db.get_operations()
        operation = operations[0]
        
        # يجب أن يحصل العامل على 100% لأنه صاحب الزبون
        self.assertEqual(operation[6], 100.0)  # نصيب العامل
        self.assertEqual(operation[7], 0.0)    # نصيب المالك
    
    def test_profit_calculation_different_owner(self):
        """اختبار حساب الأرباح عندما يكون العامل ليس صاحب الزبون"""
        # إضافة البيانات المطلوبة
        customer_id = self.db.add_customer('زبون', '***********', None, 'employee1')
        self.db.add_service('خدمة', 100.0)
        
        services = self.db.get_services()
        service_id = next(s[0] for s in services if s[1] == 'خدمة')
        
        # عامل آخر ينفذ الخدمة
        self.db.add_operation(customer_id, service_id, 'employee2', 'employee1', 100.0)
        
        operations = self.db.get_operations()
        operation = operations[0]
        
        # يجب أن يحصل العامل على 50% وصاحب الزبون على 50%
        self.assertEqual(operation[6], 50.0)  # نصيب العامل
        self.assertEqual(operation[7], 50.0)  # نصيب المالك

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("تشغيل اختبارات برنامج إدارة الحلاقة...")
    print("=" * 50)

    # إنشاء مجموعة الاختبارات باستخدام TestLoader
    loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()

    # إضافة اختبارات قاعدة البيانات
    test_suite.addTest(loader.loadTestsFromTestCase(TestDatabase))

    # إضافة اختبارات الترجمات
    test_suite.addTest(loader.loadTestsFromTestCase(TestTranslations))

    # إضافة اختبارات الإعدادات
    test_suite.addTest(loader.loadTestsFromTestCase(TestConfig))

    # إضافة اختبارات منطق العمل
    test_suite.addTest(loader.loadTestsFromTestCase(TestBusinessLogic))

    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
    else:
        print("❌ بعض الاختبارات فشلت:")
        print(f"   - فشل: {len(result.failures)}")
        print(f"   - خطأ: {len(result.errors)}")

    return result.wasSuccessful()

def run_tests_simple():
    """تشغيل الاختبارات بطريقة بسيطة (متوافقة مع جميع إصدارات Python)"""
    print("تشغيل اختبارات برنامج إدارة الحلاقة...")
    print("=" * 50)

    # تشغيل الاختبارات مباشرة
    unittest.main(verbosity=2, exit=False)

if __name__ == "__main__":
    try:
        run_tests()
    except Exception as e:
        print(f"خطأ في تشغيل الاختبارات: {e}")
        print("محاولة تشغيل الاختبارات بطريقة بديلة...")
        run_tests_simple()
