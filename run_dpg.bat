@echo off
chcp 65001 >nul
title 🚀 برنامج إدارة الحلاقة - Dear PyGui الجديد
color 0D

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              💈 برنامج إدارة الحلاقة 💈                    ██
echo ██                  Dear PyGui - الإصدار الجديد               ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    pause
    exit /b 1
)

echo 🚀 تشغيل البرنامج الجديد مع Dear PyGui...
echo.
echo ✨ المميزات الجديدة:
echo    🎨 واجهة عصرية بالكامل مع Dear PyGui
echo    🃏 نظام بطاقات متطور وسهل
echo    🌈 ألوان وثيمات حديثة
echo    ⚡ أداء أسرع وأكثر استقراراً
echo    📱 تصميم متجاوب وجميل
echo.
echo 🎯 التحسينات الرئيسية:
echo    • إزالة المكتبات القديمة (tkinter, ttkbootstrap)
echo    • استخدام Dear PyGui الحديثة والسريعة
echo    • تصميم بطاقات كامل ومنظم
echo    • ثيمات ملونة للبطاقات المختلفة
echo    • واجهة أكثر سلاسة وجمالاً
echo.
echo 👨‍💼 لوحة تحكم المدير:
echo    📊 بطاقة الإحصائيات العامة (زرقاء)
echo       • 👥 الزبائن المسجلين
echo       • ✂️ الخدمات المتاحة  
echo       • 💺 العمليات المكتملة
echo       • 👷‍♂️ العاملون النشطون
echo.
echo    🕐 بطاقة الأنشطة الحديثة (برتقالية)
echo       • آخر 5 عمليات في النظام
echo       • تفاصيل كاملة لكل عملية
echo       • زر "عرض المزيد"
echo.
echo    ⚡ بطاقة الإجراءات السريعة (خضراء)
echo       • 👤 إضافة زبون جديد
echo       • 💺 تسجيل عملية جديدة
echo       • ✂️ إدارة الخدمات
echo       • 📊 عرض التقارير
echo.
echo    🧭 بطاقة التنقل السريع (حمراء)
echo       • 👥 إدارة الزبائن
echo       • ✂️ إدارة الخدمات
echo       • 💺 إدارة العمليات
echo       • 📊 التقارير والإحصائيات
echo.
echo 👷‍♂️ لوحة تحكم العامل:
echo    📊 بطاقة إحصائياتك الشخصية (زرقاء)
echo       • 💺 إجمالي عملياتك
echo       • 💰 إجمالي أرباحك
echo       • 📅 عمليات اليوم
echo       • 🏆 أرباح اليوم
echo.
echo    💺 بطاقة عملياتك الحديثة (خضراء)
echo       • آخر 5 عمليات شخصية
echo       • تفاصيل كل عملية
echo       • زر "عرض المزيد"
echo.
echo    ⚡ بطاقة إجراءاتك السريعة (برتقالية)
echo       • 💺 تسجيل عملية جديدة
echo       • 👤 إضافة زبون جديد
echo       • 📊 عرض عملياتي
echo       • 🏆 عرض إنجازاتي
echo.
echo    🏆 بطاقة إنجازاتك (حمراء)
echo       • 🎯 البداية - أول عملية
echo       • ⭐ نجم صاعد - 10 عمليات
echo       • 🏅 محترف - 25 عملية
echo       • 💰 مربح - 500 ج.م أرباح
echo.
echo 🎨 المميزات البصرية:
echo    🌈 ثيم داكن عصري مع ألوان متدرجة
echo    🃏 بطاقات ملونة حسب النوع
echo    📱 تخطيط متجاوب وجميل
echo    ⚡ انتقالات سلسة وسريعة
echo    🎭 تأثيرات بصرية متقدمة
echo.
echo 🔐 طريقة الدخول:
echo    👨‍💼 المدير: admin / admin123
echo    👷‍♂️ العاملون: اضغط على البطاقة
echo.
echo 💡 نصائح الاستخدام:
echo    • استمتع بالواجهة الجديدة السريعة
echo    • استخدم البطاقات للوصول السريع
echo    • جرب جميع الألوان والثيمات
echo    • استفد من الأداء المحسن
echo.
echo 🚀 التقنيات المستخدمة:
echo    • Dear PyGui - واجهة رسومية حديثة
echo    • SQLite - قاعدة بيانات سريعة
echo    • Python 3.8+ - لغة برمجة قوية
echo    • تصميم بطاقات عصري
echo.
echo ═══════════════════════════════════════
echo.

REM التحقق من وجود Dear PyGui
python -c "import dearpygui.dearpygui" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  تحذير: Dear PyGui غير مثبتة
    echo 📦 جاري تثبيت المتطلبات...
    pip install -r requirements_dpg.txt
    echo ✅ تم تثبيت المتطلبات
    echo.
)

REM تشغيل البرنامج الجديد
echo 🚀 بدء تشغيل البرنامج...
python main_dpg.py

REM في حالة إغلاق البرنامج
echo.
echo 🚀 شكراً لاستخدام النسخة الجديدة!
echo 💈 Dear PyGui - سرعة وجمال في واجهة واحدة!
echo 🌟 تجربة عصرية تليق بأفضل صالونات الحلاقة!
echo.
pause
