#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام بطاقات العاملين
"""

import sys
import os

def test_employee_cards_styles():
    """اختبار أنماط بطاقات العاملين"""
    print("💳 اختبار أنماط بطاقات العاملين...")
    
    try:
        from employee_cards_styles import employee_card_styles
        
        # اختبار الأنماط
        styles = employee_card_styles.card_styles
        print(f"✅ تم تحميل {len(styles)} نمط بطاقة")
        
        # اختبار الأيقونات
        avatars = employee_card_styles.employee_avatars
        print(f"✅ تم تحميل {len(avatars)} أيقونة عامل")
        
        # اختبار التخصصات
        specialties = employee_card_styles.get_employee_specialties()
        print(f"✅ تم تحميل {len(specialties)} تخصص")
        
        # اختبار الألوان
        colors = employee_card_styles.card_colors
        print(f"✅ تم تحميل {len(colors)} مجموعة ألوان")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في أنماط البطاقات: {e}")
        return False

def test_employee_data_generation():
    """اختبار توليد بيانات العاملين"""
    print("\n📊 اختبار توليد بيانات العاملين...")
    
    try:
        from employee_cards_styles import employee_card_styles
        
        # أسماء تجريبية
        test_employees = [
            "أحمد محمد",
            "محمد علي", 
            "علي حسن",
            "حسن أحمد",
            "سامي محمود"
        ]
        
        print("🧪 اختبار البيانات المولدة:")
        
        for employee in test_employees:
            card_data = employee_card_styles.create_enhanced_card_data(employee)
            
            print(f"  👤 {employee}:")
            print(f"    🎨 النمط: {card_data['style']}")
            print(f"    👨 الأيقونة: {card_data['avatar']}")
            print(f"    🔧 التخصص: {card_data['specialty']}")
            print(f"    ⭐ التقييم: {card_data['stats']['rating']}")
            print(f"    👥 الزبائن: {card_data['stats']['total_customers']}")
            print(f"    🏆 الشارات: {len(card_data['badges'])}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في توليد البيانات: {e}")
        return False

def test_card_consistency():
    """اختبار ثبات بيانات البطاقات"""
    print("🔄 اختبار ثبات بيانات البطاقات...")
    
    try:
        from employee_cards_styles import employee_card_styles
        
        test_name = "أحمد محمد"
        
        # توليد البيانات عدة مرات
        data1 = employee_card_styles.create_enhanced_card_data(test_name)
        data2 = employee_card_styles.create_enhanced_card_data(test_name)
        data3 = employee_card_styles.create_enhanced_card_data(test_name)
        
        # التحقق من الثبات
        if (data1['style'] == data2['style'] == data3['style'] and
            data1['avatar'] == data2['avatar'] == data3['avatar'] and
            data1['specialty'] == data2['specialty'] == data3['specialty']):
            print("✅ البيانات ثابتة ومتسقة")
            return True
        else:
            print("❌ البيانات غير ثابتة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الثبات: {e}")
        return False

def test_main_app_with_cards():
    """اختبار التطبيق الرئيسي مع البطاقات"""
    print("\n🚀 اختبار التطبيق مع نظام البطاقات...")
    
    try:
        # اختبار الاستيراد
        from main import CoiffeurApp
        print("✅ تم استيراد التطبيق الرئيسي")
        
        # اختبار إنشاء التطبيق (بدون تشغيل)
        app = CoiffeurApp()
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # التحقق من وجود الدوال الجديدة
        if hasattr(app, 'load_employee_cards'):
            print("✅ دالة تحميل البطاقات موجودة")
        else:
            print("⚠️  دالة تحميل البطاقات غير موجودة")
        
        if hasattr(app, 'create_single_employee_card'):
            print("✅ دالة إنشاء البطاقة موجودة")
        else:
            print("⚠️  دالة إنشاء البطاقة غير موجودة")
        
        if hasattr(app, 'employee_card_login'):
            print("✅ دالة تسجيل دخول البطاقة موجودة")
        else:
            print("⚠️  دالة تسجيل دخول البطاقة غير موجودة")
        
        if hasattr(app, 'show_add_employee_dialog'):
            print("✅ دالة إضافة عامل موجودة")
        else:
            print("⚠️  دالة إضافة عامل غير موجودة")
        
        # إغلاق التطبيق
        app.root.destroy()
        print("✅ تم إغلاق التطبيق بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التطبيق: {e}")
        return False

def test_database_integration():
    """اختبار تكامل قاعدة البيانات"""
    print("\n🗄️ اختبار تكامل قاعدة البيانات...")
    
    try:
        from database import Database
        
        # إنشاء قاعدة بيانات تجريبية
        db = Database("test_cards.db")
        
        # اختبار إضافة عاملين
        test_employees = ["عامل تجريبي 1", "عامل تجريبي 2"]
        
        for employee in test_employees:
            try:
                db.add_user(employee, role='employee')
                print(f"✅ تم إضافة العامل: {employee}")
            except:
                print(f"⚠️  العامل موجود بالفعل: {employee}")
        
        # اختبار استرجاع العاملين
        users = db.get_users()
        employees = [user for user in users if user[1] == 'employee']
        print(f"✅ تم العثور على {len(employees)} عامل")
        
        # حذف قاعدة البيانات التجريبية
        if os.path.exists("test_cards.db"):
            os.remove("test_cards.db")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def run_visual_cards_test():
    """تشغيل اختبار بصري للبطاقات"""
    print("\n👁️ اختبار بصري لنظام البطاقات...")
    
    try:
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        from employee_cards_styles import employee_card_styles
        
        # إنشاء نافذة اختبار
        root = ttk_bs.Window(themename="superhero")
        root.title("💳 اختبار نظام البطاقات")
        root.geometry("1000x700")
        
        # إطار رئيسي
        main_frame = ttk_bs.Frame(root, padding=20)
        main_frame.pack(fill='both', expand=True)
        
        # عنوان
        title = ttk_bs.Label(
            main_frame,
            text="💳 اختبار نظام بطاقات العاملين",
            font=("Segoe UI", 20, "bold"),
            bootstyle="primary"
        )
        title.pack(pady=(0, 20))
        
        # إطار البطاقات
        cards_frame = ttk_bs.Frame(main_frame)
        cards_frame.pack(fill='both', expand=True)
        
        # عاملين تجريبيين
        test_employees = [
            "أحمد محمد", "محمد علي", "علي حسن",
            "حسن أحمد", "سامي محمود", "محمود سامي"
        ]
        
        # إنشاء البطاقات
        for i, employee in enumerate(test_employees):
            row = i // 3
            col = i % 3
            
            card_data = employee_card_styles.create_enhanced_card_data(employee)
            
            # إطار البطاقة
            card_frame = ttk_bs.LabelFrame(
                cards_frame,
                text="",
                bootstyle=card_data["style"],
                padding=15
            )
            card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
            
            # محتوى البطاقة
            ttk_bs.Label(
                card_frame,
                text=card_data["avatar"],
                font=("Segoe UI Emoji", 24),
                bootstyle=f"inverse-{card_data['style']}"
            ).pack()
            
            ttk_bs.Label(
                card_frame,
                text=employee,
                font=("Segoe UI", 12, "bold"),
                bootstyle=f"inverse-{card_data['style']}"
            ).pack(pady=5)
            
            ttk_bs.Label(
                card_frame,
                text=card_data["specialty"],
                font=("Segoe UI", 10),
                bootstyle=f"inverse-{card_data['style']}"
            ).pack()
            
            ttk_bs.Label(
                card_frame,
                text=f"⭐ {card_data['stats']['rating']} | 👥 {card_data['stats']['total_customers']}",
                font=("Segoe UI", 9),
                bootstyle=f"inverse-{card_data['style']}"
            ).pack(pady=5)
        
        # تكوين الشبكة
        for i in range(3):
            cards_frame.grid_columnconfigure(i, weight=1)
        
        # زر الإغلاق
        close_btn = ttk_bs.Button(
            main_frame,
            text="✅ اختبار مكتمل - إغلاق",
            command=root.destroy,
            bootstyle="success",
            width=30
        )
        close_btn.pack(pady=20)
        
        print("✅ تم فتح نافذة اختبار البطاقات")
        print("💡 أغلق النافذة للمتابعة...")
        
        # تشغيل النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار البصري: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار نظام بطاقات العاملين - برنامج إدارة الحلاقة")
    print("=" * 70)
    
    tests = [
        ("أنماط البطاقات", test_employee_cards_styles),
        ("توليد البيانات", test_employee_data_generation),
        ("ثبات البيانات", test_card_consistency),
        ("تكامل قاعدة البيانات", test_database_integration),
        ("التطبيق الرئيسي", test_main_app_with_cards),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات نظام البطاقات نجحت!")
        
        # تشغيل الاختبار البصري
        visual_test = input("\nهل تريد تشغيل الاختبار البصري للبطاقات؟ (y/n): ")
        if visual_test.lower() in ['y', 'yes', 'نعم']:
            run_visual_cards_test()
        
        print("\n✅ نظام بطاقات العاملين جاهز للاستخدام!")
        print("🚀 يمكنك الآن تشغيل البرنامج: python clean_run.py")
        print("💳 أو استخدم: run_with_cards.bat")
        
    else:
        print("⚠️  بعض اختبارات البطاقات فشلت. يرجى مراجعة الأخطاء.")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
