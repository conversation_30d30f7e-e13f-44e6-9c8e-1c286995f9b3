#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسينات واجهة المستخدم لبرنامج إدارة الحلاقة
تأثيرات بصرية وتحسينات تفاعلية
"""

import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from PIL import Image, ImageTk, ImageDraw, ImageFilter
import threading
import time

class UIEnhancements:
    """فئة تحسينات واجهة المستخدم"""
    
    def __init__(self):
        self.animations = {}
        self.hover_effects = {}
    
    def add_hover_effect(self, widget, hover_color=None, normal_color=None):
        """إضافة تأثير التمرير للعناصر"""
        if not hover_color:
            hover_color = "#E74C3C"
        if not normal_color:
            normal_color = widget.cget("background")
        
        def on_enter(event):
            widget.configure(background=hover_color)
        
        def on_leave(event):
            widget.configure(background=normal_color)
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    def add_button_animation(self, button):
        """إضافة تأثير الضغط للأزرار"""
        original_relief = button.cget("relief")
        
        def on_press(event):
            button.configure(relief="sunken")
        
        def on_release(event):
            button.configure(relief=original_relief)
        
        button.bind("<Button-1>", on_press)
        button.bind("<ButtonRelease-1>", on_release)
    
    def create_rounded_button(self, parent, text, command=None, **kwargs):
        """إنشاء زر مدور عصري"""
        # هذه دالة مبسطة - في التطبيق الحقيقي نحتاج مكتبة إضافية للأشكال المدورة
        button = ttk_bs.Button(
            parent,
            text=text,
            command=command,
            style="Modern.TButton",
            **kwargs
        )
        
        # إضافة تأثيرات
        self.add_button_animation(button)
        
        return button
    
    def create_card_frame(self, parent, title=None, **kwargs):
        """إنشاء إطار بطاقة عصري"""
        card = ttk_bs.LabelFrame(
            parent,
            text=title if title else "",
            style="Card.TLabelFrame",
            padding=15,
            **kwargs
        )
        
        return card
    
    def create_gradient_label(self, parent, text, gradient_colors=None):
        """إنشاء تسمية بتدرج لوني"""
        if not gradient_colors:
            gradient_colors = ["#E74C3C", "#C0392B"]
        
        # تطبيق مبسط للتدرج
        label = ttk_bs.Label(
            parent,
            text=text,
            style="ModernTitle.TLabel"
        )
        
        return label
    
    def add_loading_animation(self, widget):
        """إضافة تأثير التحميل"""
        loading_text = ["⏳", "⌛", "⏳", "⌛"]
        current_index = 0
        
        def animate():
            nonlocal current_index
            if widget.winfo_exists():
                original_text = widget.cget("text")
                widget.configure(text=f"{loading_text[current_index]} {original_text}")
                current_index = (current_index + 1) % len(loading_text)
                widget.after(500, animate)
        
        animate()
    
    def create_notification(self, parent, message, notification_type="info", duration=3000):
        """إنشاء إشعار منبثق"""
        colors = {
            "success": "#27AE60",
            "error": "#E74C3C",
            "warning": "#F39C12",
            "info": "#3498DB"
        }
        
        icons = {
            "success": "✅",
            "error": "❌",
            "warning": "⚠️",
            "info": "ℹ️"
        }
        
        # إنشاء إطار الإشعار
        notification = ttk_bs.Frame(parent, bootstyle=notification_type)
        notification.place(relx=0.5, rely=0.1, anchor="center")
        
        # محتوى الإشعار
        content_frame = ttk_bs.Frame(notification, bootstyle=notification_type)
        content_frame.pack(padx=20, pady=10)
        
        icon_label = ttk_bs.Label(
            content_frame,
            text=icons.get(notification_type, "ℹ️"),
            font=("Segoe UI Emoji", 16),
            bootstyle=f"inverse-{notification_type}"
        )
        icon_label.pack(side="left", padx=(0, 10))
        
        message_label = ttk_bs.Label(
            content_frame,
            text=message,
            font=("Segoe UI", 12),
            bootstyle=f"inverse-{notification_type}"
        )
        message_label.pack(side="left")
        
        # إخفاء الإشعار تلقائياً
        def hide_notification():
            try:
                notification.destroy()
            except:
                pass
        
        parent.after(duration, hide_notification)
        
        return notification
    
    def create_progress_bar(self, parent, **kwargs):
        """إنشاء شريط تقدم عصري"""
        progress = ttk_bs.Progressbar(
            parent,
            style="Modern.Horizontal.TProgressbar",
            **kwargs
        )
        
        return progress
    
    def animate_widget_entrance(self, widget, animation_type="fade_in"):
        """تحريك دخول العنصر"""
        if animation_type == "fade_in":
            self._fade_in_animation(widget)
        elif animation_type == "slide_in":
            self._slide_in_animation(widget)
    
    def _fade_in_animation(self, widget):
        """تأثير الظهور التدريجي"""
        # تطبيق مبسط
        widget.configure(state="normal")
    
    def _slide_in_animation(self, widget):
        """تأثير الانزلاق"""
        # تطبيق مبسط
        widget.configure(state="normal")
    
    def create_modern_treeview(self, parent, columns, **kwargs):
        """إنشاء جدول عصري"""
        # إطار الجدول
        tree_frame = ttk_bs.Frame(parent)
        
        # الجدول
        tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            style="Modern.Treeview",
            **kwargs
        )
        
        # أشرطة التمرير
        v_scrollbar = ttk_bs.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
        h_scrollbar = ttk_bs.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
        
        tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # تأثيرات الصفوف
        tree.tag_configure("evenrow", background="#F8F9FA")
        tree.tag_configure("oddrow", background="#FFFFFF")
        tree.tag_configure("selected", background="#E74C3C", foreground="white")
        
        return tree_frame, tree
    
    def create_search_box(self, parent, placeholder="البحث...", **kwargs):
        """إنشاء صندوق بحث عصري"""
        search_frame = ttk_bs.Frame(parent)
        
        # أيقونة البحث
        search_icon = ttk_bs.Label(
            search_frame,
            text="🔍",
            font=("Segoe UI Emoji", 14)
        )
        search_icon.pack(side="left", padx=(10, 5))
        
        # حقل البحث
        search_entry = ttk_bs.Entry(
            search_frame,
            font=("Segoe UI", 12),
            style="Modern.TEntry",
            **kwargs
        )
        search_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        # إضافة placeholder
        search_entry.insert(0, placeholder)
        search_entry.configure(foreground="gray")
        
        def on_focus_in(event):
            if search_entry.get() == placeholder:
                search_entry.delete(0, "end")
                search_entry.configure(foreground="black")
        
        def on_focus_out(event):
            if not search_entry.get():
                search_entry.insert(0, placeholder)
                search_entry.configure(foreground="gray")
        
        search_entry.bind("<FocusIn>", on_focus_in)
        search_entry.bind("<FocusOut>", on_focus_out)
        
        return search_frame, search_entry
    
    def create_stats_card(self, parent, title, value, icon, color="primary"):
        """إنشاء بطاقة إحصائيات"""
        card = ttk_bs.LabelFrame(
            parent,
            text=f"{icon} {title}",
            bootstyle=color,
            padding=20
        )
        
        value_label = ttk_bs.Label(
            card,
            text=str(value),
            font=("Segoe UI", 24, "bold"),
            bootstyle=f"inverse-{color}"
        )
        value_label.pack()
        
        return card
    
    def apply_theme_to_widget(self, widget, theme_style):
        """تطبيق ثيم على عنصر"""
        try:
            widget.configure(style=theme_style)
        except:
            pass

# إنشاء مثيل عام للتحسينات
ui_enhancements = UIEnhancements()
