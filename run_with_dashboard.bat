@echo off
chcp 65001 >nul
title 📊 برنامج إدارة الحلاقة - لوحة التحكم المتطورة
color 0E

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              💈 برنامج إدارة الحلاقة 💈                    ██
echo ██                  لوحة التحكم المتطورة                     ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    pause
    exit /b 1
)

echo 📊 تشغيل البرنامج مع لوحة التحكم المتطورة...
echo.
echo ✨ مكونات لوحة التحكم الجديدة:
echo    🏠 رأس رئيسي مع الوقت الحالي
echo    👋 رسالة ترحيب شخصية
echo    📈 8 بطاقات إحصائية متطورة
echo    🕐 قسم الأنشطة الحديثة
echo    ⚡ 6 إجراءات سريعة محسنة
echo    💈 معلومات شاملة للصالون
echo    ⚙️ شاشة إعدادات متكاملة
echo.
echo 📊 الإحصائيات المتاحة:
echo    👥 إجمالي الزبائن المسجلين
echo    ✂️ عدد الخدمات المتاحة
echo    💺 العمليات المنجزة
echo    👷‍♂️ عدد العاملين النشطين
echo    💰 الأرباح التقديرية
echo    📅 عمليات اليوم
echo    ⭐ متوسط التقييم (4.8/5)
echo    🏆 حالة الصالون (ممتاز)
echo.
echo ⚡ الإجراءات السريعة:
echo    👤 إضافة زبون جديد
echo    💺 تسجيل عملية جديدة
echo    ✂️ إدارة الخدمات
echo    📊 عرض التقارير
echo    👥 إدارة الزبائن
echo    ⚙️ إعدادات النظام
echo.
echo 🕐 الأنشطة الحديثة:
echo    • عرض آخر 5 عمليات
echo    • تفاصيل كاملة لكل عملية
echo    • تصميم متناوب للوضوح
echo    • تحديث تلقائي للبيانات
echo.
echo 💈 معلومات الصالون:
echo    🏪 اسم الصالون: صالون الحلاقة العصري
echo    📍 العنوان: شارع الجمهورية، المدينة
echo    📞 الهاتف: +20 123 456 789
echo    🕐 ساعات العمل: 9:00 ص - 10:00 م
echo.
echo ⚙️ الإعدادات المتاحة:
echo    🌐 تغيير اللغة (عربي/فرنسي)
echo    💾 إنشاء نسخة احتياطية
echo    📥 استعادة نسخة احتياطية
echo    💻 معلومات النظام
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👨‍💼 المدير: admin / admin123
echo    👷‍♂️ العاملون: اضغط على البطاقة
echo.
echo 💡 نصائح الاستخدام:
echo    • استخدم التمرير لرؤية جميع الأقسام
echo    • راجع الإحصائيات يومياً
echo    • تابع الأنشطة الحديثة
echo    • استخدم الإجراءات السريعة
echo    • أنشئ نسخ احتياطية دورية
echo.
echo ═══════════════════════════════════════
echo.

REM تشغيل البرنامج
python clean_run.py

REM في حالة إغلاق البرنامج
echo.
echo 📊 شكراً لاستخدام لوحة التحكم المتطورة!
echo 💈 نأمل أن تكون قد استفدت من المعلومات الشاملة!
echo.
pause
