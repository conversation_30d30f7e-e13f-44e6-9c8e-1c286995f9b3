# برنامج إدارة الحلاقة - Coiffeur Management System

برنامج شامل لإدارة عمليات الحلاقة والزبائن والخدمات والتقارير، مكتوب بلغة Python باستخدام مكتبة Tkinter مع ttkbootstrap للتصميم الحديث.

## المميزات

### 🔐 نظام تسجيل الدخول
- **المدير**: اسم المستخدم "admin" وكلمة المرور "admin123"
- **العاملون**: تسجيل دخول بالاسم فقط (بدون كلمة مرور)
- إضافة العاملين تلقائياً عند أول تسجيل دخول

### 🌐 دعم متعدد اللغات
- واجهة باللغة العربية والفرنسية
- إمكانية التبديل بين اللغات أثناء التشغيل
- دعم النصوص العربية في التقارير

### 👥 إدارة الزبائن
- إضافة زبائن جدد مع الاسم ورقم الهاتف
- إمكانية إضافة صورة للزبون
- تسجيل من أضاف كل زبون
- عرض قائمة شاملة بجميع الزبائن

### 💼 إدارة الخدمات
- إضافة وتعديل الخدمات (للمدير فقط)
- تحديد أسعار الخدمات
- خدمات افتراضية جاهزة

### 📊 تسجيل العمليات
- تسجيل عمليات الحلاقة مع تفاصيل كاملة
- اختيار الزبون والخدمة ومن نفذها
- حساب تلقائي للأرباح:
  - 100% للعامل إذا كان صاحب الزبون
  - 50% للعامل و 50% للمالك إذا لم يكن صاحب الزبون

### 📈 نظام التقارير
- تقارير يومية وشهرية
- تقارير مخصصة لكل مستخدم
- تقارير شاملة للمدير
- إحصائيات مفصلة (عدد العمليات، الإيرادات، الأرباح)
- تصدير التقارير إلى PDF و Excel

### 🎨 تصميم حديث
- واجهة عصرية باستخدام ttkbootstrap
- ألوان متناسقة وأيقونات واضحة
- تجربة مستخدم سهلة ومريحة

## متطلبات النظام

- Windows 7 أو أحدث
- Python 3.8+ (للتشغيل من المصدر)
- 100 MB مساحة فارغة على القرص الصلب

## التثبيت والتشغيل

### الطريقة الأولى: تشغيل من المصدر

1. **تثبيت Python**:
   - تحميل Python من [python.org](https://python.org)
   - التأكد من إضافة Python إلى PATH

2. **تثبيت المكتبات المطلوبة**:
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل البرنامج**:
   ```bash
   python run.py
   ```

### الطريقة الثانية: بناء ملف EXE

1. **بناء الملف التنفيذي**:
   ```bash
   python build_exe.py
   ```

2. **تثبيت البرنامج**:
   - الانتقال إلى مجلد `dist`
   - تشغيل `install.bat`
   - سيتم إنشاء اختصار على سطح المكتب

## بنية المشروع

```
coiffeur/
├── main.py              # الواجهة الرئيسية للبرنامج
├── database.py          # إدارة قاعدة البيانات SQLite
├── translations.py      # ملف الترجمات (عربي/فرنسي)
├── reports.py           # مولد التقارير (PDF/Excel)
├── run.py              # ملف التشغيل الرئيسي
├── build_exe.py        # سكريبت بناء ملف EXE
├── requirements.txt    # قائمة المكتبات المطلوبة
├── coiffeur.db         # قاعدة البيانات (تُنشأ تلقائياً)
└── README.md           # هذا الملف
```

## استخدام البرنامج

### 1. تسجيل الدخول
- **للمدير**: استخدم "admin" و "admin123"
- **للعاملين**: أدخل اسم المستخدم فقط

### 2. إدارة الزبائن
- انقر على "الزبائن" من القائمة الجانبية
- اضغط "إضافة زبون" لإضافة زبون جديد
- املأ البيانات المطلوبة واختر صورة (اختيارية)

### 3. إدارة الخدمات
- انقر على "الخدمات" من القائمة الجانبية
- المدير فقط يمكنه إضافة خدمات جديدة
- حدد اسم الخدمة والسعر

### 4. تسجيل العمليات
- انقر على "العمليات" من القائمة الجانبية
- اضغط "عملية جديدة"
- اختر الزبون والخدمة ومن نفذها وصاحب الزبون
- سيتم حساب الأرباح تلقائياً

### 5. عرض التقارير
- انقر على "التقارير" من القائمة الجانبية
- حدد التاريخ والمستخدم (للمدير)
- اضغط "إنشاء التقرير"
- يمكن تصدير التقرير إلى PDF أو Excel

## قاعدة البيانات

يستخدم البرنامج قاعدة بيانات SQLite محلية تحتوي على:

- **users**: المستخدمون (مدير وعاملون)
- **customers**: بيانات الزبائن
- **services**: الخدمات المتاحة
- **operations**: سجل العمليات والأرباح

## الأمان

- كلمات المرور مخزنة كنص عادي (للبساطة)
- قاعدة البيانات محلية وآمنة
- لا يتطلب اتصال بالإنترنت

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل:
- تحقق من ملف `coiffeur.db` موجود
- تأكد من تثبيت جميع المكتبات المطلوبة
- راجع رسائل الخطأ في وحدة التحكم

## الترخيص

هذا البرنامج مجاني للاستخدام الشخصي والتجاري.

## التطوير المستقبلي

- إضافة نسخ احتياطي تلقائي
- تحسين واجهة المستخدم
- إضافة المزيد من التقارير
- دعم طباعة الفواتير
- إضافة نظام المواعيد

---

**تم تطوير البرنامج بواسطة Augment Agent - 2025**
