import sqlite3
import os
from datetime import datetime

class Database:
    def __init__(self, db_path="coiffeur.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT,
                role TEXT NOT NULL DEFAULT 'employee',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الزبائن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                image_path TEXT,
                added_by TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الخدمات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS services (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العمليات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS operations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                service_id INTEGER NOT NULL,
                performed_by TEXT NOT NULL,
                customer_owner TEXT NOT NULL,
                price REAL NOT NULL,
                employee_share REAL NOT NULL,
                owner_share REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (service_id) REFERENCES services (id)
            )
        ''')
        
        # إدراج المدير الافتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, role) 
            VALUES ('admin', 'admin123', 'admin')
        ''')
        
        # إدراج خدمات افتراضية
        default_services = [
            ('قص شعر عادي', 50.0),
            ('قص شعر + حلاقة', 80.0),
            ('حلاقة فقط', 30.0),
            ('تصفيف شعر', 40.0),
            ('صبغة شعر', 150.0)
        ]
        
        for service_name, price in default_services:
            cursor.execute('''
                INSERT OR IGNORE INTO services (name, price) 
                VALUES (?, ?)
            ''', (service_name, price))
        
        conn.commit()
        conn.close()
    
    def add_user(self, username, password=None, role='employee'):
        """إضافة مستخدم جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO users (username, password, role) 
                VALUES (?, ?, ?)
            ''', (username, password, role))
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def authenticate_user(self, username, password=None):
        """التحقق من صحة بيانات المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if password:  # للمدير
            cursor.execute('''
                SELECT username, role FROM users 
                WHERE username = ? AND password = ?
            ''', (username, password))
        else:  # للعاملين
            cursor.execute('''
                SELECT username, role FROM users 
                WHERE username = ? AND role = 'employee'
            ''', (username,))
        
        result = cursor.fetchone()
        conn.close()
        return result
    
    def get_users(self):
        """الحصول على قائمة المستخدمين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT username, role FROM users ORDER BY username')
        result = cursor.fetchall()
        conn.close()
        return result
    
    def add_customer(self, name, phone, image_path, added_by):
        """إضافة زبون جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO customers (name, phone, image_path, added_by) 
            VALUES (?, ?, ?, ?)
        ''', (name, phone, image_path, added_by))
        customer_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return customer_id
    
    def get_customers(self):
        """الحصول على قائمة الزبائن"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, name, phone, image_path, added_by, created_at 
            FROM customers ORDER BY name
        ''')
        result = cursor.fetchall()
        conn.close()
        return result
    
    def add_service(self, name, price):
        """إضافة خدمة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO services (name, price) VALUES (?, ?)
        ''', (name, price))
        conn.commit()
        conn.close()
    
    def get_services(self):
        """الحصول على قائمة الخدمات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, name, price FROM services ORDER BY name')
        result = cursor.fetchall()
        conn.close()
        return result
    
    def add_operation(self, customer_id, service_id, performed_by, customer_owner, price):
        """تسجيل عملية حلاقة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # حساب الأرباح
        if performed_by == customer_owner:
            # إذا كان العامل هو صاحب الزبون، يأخذ 100%
            employee_share = price
            owner_share = 0
        else:
            # إذا كان العامل ليس صاحب الزبون، يأخذ 50%
            employee_share = price * 0.5
            owner_share = price * 0.5
        
        cursor.execute('''
            INSERT INTO operations 
            (customer_id, service_id, performed_by, customer_owner, price, employee_share, owner_share) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (customer_id, service_id, performed_by, customer_owner, price, employee_share, owner_share))
        
        conn.commit()
        conn.close()
    
    def get_operations(self, start_date=None, end_date=None, user=None):
        """الحصول على العمليات مع إمكانية التصفية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT o.id, c.name as customer_name, s.name as service_name, 
                   o.performed_by, o.customer_owner, o.price, 
                   o.employee_share, o.owner_share, o.created_at
            FROM operations o
            JOIN customers c ON o.customer_id = c.id
            JOIN services s ON o.service_id = s.id
        '''
        
        conditions = []
        params = []
        
        if start_date:
            conditions.append("DATE(o.created_at) >= ?")
            params.append(start_date)
        
        if end_date:
            conditions.append("DATE(o.created_at) <= ?")
            params.append(end_date)
        
        if user:
            conditions.append("(o.performed_by = ? OR o.customer_owner = ?)")
            params.extend([user, user])
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY o.created_at DESC"
        
        cursor.execute(query, params)
        result = cursor.fetchall()
        conn.close()
        return result
