#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تصميم بطاقات العاملين المتطور والأنيق
تصميمات احترافية مع تأثيرات بصرية متقدمة
"""

import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from PIL import Image, ImageTk, ImageDraw, ImageFilter
import random
from modern_theme import modern_theme

class PremiumCardDesign:
    """فئة تصميم البطاقات المتطورة"""
    
    def __init__(self):
        self.setup_premium_styles()
        self.setup_card_gradients()
        self.setup_premium_colors()
        self.setup_card_animations()
    
    def setup_premium_styles(self):
        """إعداد الأنماط المتطورة"""
        self.premium_styles = {
            "glass": {
                "name": "زجاجي شفاف",
                "bg": "#FFFFFF",
                "bg_alpha": 0.15,
                "border": "#FFFFFF",
                "border_alpha": 0.3,
                "shadow": True,
                "blur": True
            },
            "neon": {
                "name": "نيون متوهج",
                "bg": "#1a1a2e",
                "glow_color": "#00ff88",
                "border": "#00ff88",
                "shadow": True,
                "glow": True
            },
            "gradient": {
                "name": "متدرج ملون",
                "bg_start": "#667eea",
                "bg_end": "#764ba2",
                "border": "#FFFFFF",
                "shadow": True,
                "gradient": True
            },
            "metallic": {
                "name": "معدني لامع",
                "bg": "#C0C0C0",
                "highlight": "#FFFFFF",
                "shadow_color": "#808080",
                "border": "#A0A0A0",
                "metallic": True
            },
            "modern_flat": {
                "name": "مسطح عصري",
                "bg": "#2c3e50",
                "accent": "#e74c3c",
                "text": "#ecf0f1",
                "border": "none",
                "flat": True
            },
            "card_3d": {
                "name": "ثلاثي الأبعاد",
                "bg": "#34495e",
                "highlight": "#5d6d7e",
                "shadow": "#1b2631",
                "border": "#85929e",
                "three_d": True
            }
        }
    
    def setup_card_gradients(self):
        """إعداد التدرجات اللونية المتطورة"""
        self.gradients = {
            "sunset": ["#ff7e5f", "#feb47b"],
            "ocean": ["#667eea", "#764ba2"], 
            "forest": ["#11998e", "#38ef7d"],
            "fire": ["#f12711", "#f5af19"],
            "purple": ["#667eea", "#764ba2"],
            "pink": ["#f093fb", "#f5576c"],
            "blue": ["#4facfe", "#00f2fe"],
            "green": ["#43e97b", "#38f9d7"],
            "orange": ["#fa709a", "#fee140"],
            "dark": ["#2c3e50", "#34495e"]
        }
    
    def setup_premium_colors(self):
        """إعداد الألوان المتطورة"""
        self.premium_colors = {
            "royal_blue": {
                "primary": "#1e3c72",
                "secondary": "#2a5298", 
                "accent": "#ffffff",
                "text": "#ffffff"
            },
            "emerald": {
                "primary": "#11998e",
                "secondary": "#38ef7d",
                "accent": "#ffffff", 
                "text": "#ffffff"
            },
            "ruby": {
                "primary": "#c31432",
                "secondary": "#240b36",
                "accent": "#ffffff",
                "text": "#ffffff"
            },
            "gold": {
                "primary": "#f7971e",
                "secondary": "#ffd200",
                "accent": "#2c3e50",
                "text": "#2c3e50"
            },
            "platinum": {
                "primary": "#bdc3c7",
                "secondary": "#2c3e50",
                "accent": "#2c3e50",
                "text": "#2c3e50"
            },
            "diamond": {
                "primary": "#e8f5e8",
                "secondary": "#a8e6cf",
                "accent": "#2c3e50",
                "text": "#2c3e50"
            }
        }
    
    def setup_card_animations(self):
        """إعداد الحركات والتأثيرات"""
        self.animations = {
            "hover_scale": 1.05,
            "hover_shadow": "0 8px 25px rgba(0,0,0,0.3)",
            "transition_duration": 300,
            "bounce_effect": True,
            "glow_effect": True,
            "rotate_slight": 2  # درجات
        }
    
    def get_employee_premium_style(self, employee_name):
        """الحصول على نمط متطور للعامل"""
        styles = list(self.premium_styles.keys())
        colors = list(self.premium_colors.keys())
        gradients = list(self.gradients.keys())
        
        name_hash = hash(employee_name)
        
        return {
            "style": styles[abs(name_hash) % len(styles)],
            "color_scheme": colors[abs(name_hash) % len(colors)],
            "gradient": gradients[abs(name_hash) % len(gradients)],
            "animation": "hover_scale"
        }
    
    def create_premium_card_frame(self, parent, employee_data, width=280, height=320):
        """إنشاء إطار بطاقة متطور"""
        # الحصول على النمط المتطور
        premium_style = self.get_employee_premium_style(employee_data["name"])
        color_scheme = self.premium_colors[premium_style["color_scheme"]]
        
        # إطار البطاقة الخارجي مع تأثير الظل
        outer_frame = tk.Frame(
            parent,
            bg=parent.cget('bg'),
            relief='flat',
            bd=0
        )
        outer_frame.pack(side='left', padx=15, pady=15)
        
        # إطار البطاقة الرئيسي
        card_frame = tk.Frame(
            outer_frame,
            bg=color_scheme["primary"],
            relief='raised',
            bd=2,
            width=width,
            height=height
        )
        card_frame.pack_propagate(False)
        card_frame.pack()
        
        return card_frame, color_scheme, premium_style
    
    def add_card_header(self, card_frame, employee_data, color_scheme):
        """إضافة رأس البطاقة"""
        # منطقة الرأس مع تدرج
        header_frame = tk.Frame(
            card_frame,
            bg=color_scheme["secondary"],
            height=80
        )
        header_frame.pack(fill='x', padx=3, pady=(3, 0))
        header_frame.pack_propagate(False)
        
        # أيقونة العامل الكبيرة
        avatar_label = tk.Label(
            header_frame,
            text=employee_data["avatar"],
            font=('Segoe UI Emoji', 32),
            bg=color_scheme["secondary"],
            fg=color_scheme["accent"]
        )
        avatar_label.pack(pady=15)
        
        return header_frame
    
    def add_card_body(self, card_frame, employee_data, color_scheme):
        """إضافة جسم البطاقة"""
        # منطقة المحتوى الرئيسي
        body_frame = tk.Frame(
            card_frame,
            bg=color_scheme["primary"]
        )
        body_frame.pack(fill='both', expand=True, padx=3, pady=3)
        
        # اسم العامل مع خط أنيق
        name_label = tk.Label(
            body_frame,
            text=employee_data["name"],
            font=('Segoe UI', 16, 'bold'),
            bg=color_scheme["primary"],
            fg=color_scheme["text"],
            wraplength=250
        )
        name_label.pack(pady=(15, 5))
        
        # خط فاصل أنيق
        separator = tk.Frame(
            body_frame,
            bg=color_scheme["accent"],
            height=2
        )
        separator.pack(fill='x', padx=40, pady=5)
        
        # تخصص العامل
        specialty_label = tk.Label(
            body_frame,
            text=f"🔧 {employee_data['specialty']}",
            font=('Segoe UI', 11),
            bg=color_scheme["primary"],
            fg=color_scheme["text"],
            wraplength=250
        )
        specialty_label.pack(pady=5)
        
        # إطار الإحصائيات مع تصميم أنيق
        stats_frame = tk.Frame(
            body_frame,
            bg=color_scheme["secondary"],
            relief='sunken',
            bd=1
        )
        stats_frame.pack(fill='x', padx=15, pady=10)
        
        # الإحصائيات في صفين
        stats_row1 = tk.Frame(stats_frame, bg=color_scheme["secondary"])
        stats_row1.pack(fill='x', pady=5)
        
        stats_row2 = tk.Frame(stats_frame, bg=color_scheme["secondary"])
        stats_row2.pack(fill='x', pady=5)
        
        # التقييم والزبائن
        rating_label = tk.Label(
            stats_row1,
            text=f"⭐ {employee_data['stats']['rating']}",
            font=('Segoe UI', 10, 'bold'),
            bg=color_scheme["secondary"],
            fg=color_scheme["accent"]
        )
        rating_label.pack(side='left', padx=10)
        
        customers_label = tk.Label(
            stats_row1,
            text=f"👥 {employee_data['stats']['total_customers']}",
            font=('Segoe UI', 10, 'bold'),
            bg=color_scheme["secondary"],
            fg=color_scheme["accent"]
        )
        customers_label.pack(side='right', padx=10)
        
        # الخبرة والخدمات
        experience_label = tk.Label(
            stats_row2,
            text=f"🏆 {employee_data['stats']['experience_years']} سنوات",
            font=('Segoe UI', 9),
            bg=color_scheme["secondary"],
            fg=color_scheme["accent"]
        )
        experience_label.pack(side='left', padx=10)
        
        services_label = tk.Label(
            stats_row2,
            text=f"✅ {employee_data['stats']['completed_services']}",
            font=('Segoe UI', 9),
            bg=color_scheme["secondary"],
            fg=color_scheme["accent"]
        )
        services_label.pack(side='right', padx=10)
        
        return body_frame
    
    def add_card_footer(self, card_frame, employee_data, color_scheme, login_command):
        """إضافة تذييل البطاقة"""
        # منطقة التذييل
        footer_frame = tk.Frame(
            card_frame,
            bg=color_scheme["primary"],
            height=60
        )
        footer_frame.pack(fill='x', padx=3, pady=(0, 3))
        footer_frame.pack_propagate(False)
        
        # شارات العامل
        if employee_data.get("badges"):
            badges_frame = tk.Frame(footer_frame, bg=color_scheme["primary"])
            badges_frame.pack(pady=(5, 0))
            
            for badge in employee_data["badges"][:2]:  # أول شارتين فقط
                badge_label = tk.Label(
                    badges_frame,
                    text=f"{badge['icon']}",
                    font=('Segoe UI Emoji', 12),
                    bg=color_scheme["primary"],
                    fg=color_scheme["accent"]
                )
                badge_label.pack(side='left', padx=3)
        
        # زر تسجيل الدخول الأنيق
        login_btn = tk.Button(
            footer_frame,
            text="🔑 دخول",
            font=('Segoe UI', 11, 'bold'),
            bg=color_scheme["accent"],
            fg=color_scheme["primary"],
            activebackground=color_scheme["secondary"],
            activeforeground=color_scheme["accent"],
            relief='raised',
            bd=2,
            padx=20,
            pady=5,
            cursor='hand2',
            command=login_command
        )
        login_btn.pack(pady=10)
        
        return footer_frame, login_btn
    
    def add_premium_hover_effects(self, card_frame, login_btn, color_scheme):
        """إضافة تأثيرات التمرير المتطورة"""
        original_bg = card_frame.cget('bg')
        original_relief = card_frame.cget('relief')
        
        def on_enter(event):
            # تأثير الإضاءة
            card_frame.configure(
                bg=color_scheme["secondary"],
                relief='raised',
                bd=4
            )
            # تأثير على الزر
            login_btn.configure(
                bg=color_scheme["secondary"],
                fg=color_scheme["accent"],
                relief='raised',
                bd=3
            )
        
        def on_leave(event):
            # العودة للحالة الأصلية
            card_frame.configure(
                bg=original_bg,
                relief=original_relief,
                bd=2
            )
            # إعادة تعيين الزر
            login_btn.configure(
                bg=color_scheme["accent"],
                fg=color_scheme["primary"],
                relief='raised',
                bd=2
            )
        
        # ربط الأحداث
        card_frame.bind("<Enter>", on_enter)
        card_frame.bind("<Leave>", on_leave)
        login_btn.bind("<Enter>", on_enter)
        login_btn.bind("<Leave>", on_leave)
        
        # ربط الأحداث لجميع العناصر الفرعية
        for child in card_frame.winfo_children():
            child.bind("<Enter>", on_enter)
            child.bind("<Leave>", on_leave)
            # ربط العناصر الفرعية للعناصر الفرعية
            for grandchild in child.winfo_children():
                grandchild.bind("<Enter>", on_enter)
                grandchild.bind("<Leave>", on_leave)
                for greatgrandchild in grandchild.winfo_children():
                    greatgrandchild.bind("<Enter>", on_enter)
                    greatgrandchild.bind("<Leave>", on_leave)
    
    def create_complete_premium_card(self, parent, employee_data, login_command):
        """إنشاء بطاقة متطورة كاملة"""
        # إنشاء إطار البطاقة
        card_frame, color_scheme, premium_style = self.create_premium_card_frame(
            parent, employee_data
        )
        
        # إضافة أجزاء البطاقة
        header = self.add_card_header(card_frame, employee_data, color_scheme)
        body = self.add_card_body(card_frame, employee_data, color_scheme)
        footer, login_btn = self.add_card_footer(
            card_frame, employee_data, color_scheme, login_command
        )
        
        # إضافة التأثيرات المتطورة
        self.add_premium_hover_effects(card_frame, login_btn, color_scheme)
        
        return card_frame

# إنشاء مثيل عام للتصميم المتطور
premium_card_design = PremiumCardDesign()
