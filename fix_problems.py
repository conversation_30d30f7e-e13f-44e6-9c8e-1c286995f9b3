#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح مشاكل برنامج إدارة الحلاقة
تشخص وتحل المشاكل الشائعة تلقائياً
"""

import subprocess
import sys
import os
import importlib

def run_command(command):
    """تشغيل أمر وإرجاع النتيجة"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_python():
    """فحص Python"""
    print("🔍 فحص Python...")
    version = sys.version_info
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("⚠️  تحذير: يُنصح بـ Python 3.8 أو أحدث")
        return False
    return True

def fix_pip():
    """إصلاح وترقية pip"""
    print("\n🔧 ترقية pip...")
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install --upgrade pip")
    if success:
        print("✅ تم ترقية pip بنجاح")
    else:
        print(f"⚠️  تحذير في ترقية pip: {stderr}")
    return success

def install_package(package_name, alternative_name=None):
    """تثبيت مكتبة مع معالجة الأخطاء"""
    print(f"📦 تثبيت {package_name}...")
    
    # محاولة التثبيت العادي
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install {package_name}")
    
    if success:
        print(f"✅ تم تثبيت {package_name} بنجاح")
        return True
    
    # محاولة مع --user
    print(f"🔄 محاولة تثبيت {package_name} مع --user...")
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install --user {package_name}")
    
    if success:
        print(f"✅ تم تثبيت {package_name} بنجاح (user)")
        return True
    
    # محاولة الاسم البديل
    if alternative_name:
        print(f"🔄 محاولة تثبيت {alternative_name}...")
        success, stdout, stderr = run_command(f"{sys.executable} -m pip install {alternative_name}")
        if success:
            print(f"✅ تم تثبيت {alternative_name} بنجاح")
            return True
    
    print(f"❌ فشل في تثبيت {package_name}")
    print(f"خطأ: {stderr}")
    return False

def check_and_install_packages():
    """فحص وتثبيت المكتبات المطلوبة"""
    print("\n🔍 فحص وتثبيت المكتبات...")
    
    packages = [
        ("ttkbootstrap", "ttkbootstrap"),
        ("Pillow", "PIL"),
        ("reportlab", "reportlab"),
        ("openpyxl", "openpyxl"),
        ("python-bidi", "bidi"),
        ("arabic-reshaper", "arabic_reshaper")
    ]
    
    missing_packages = []
    
    for package_name, import_name in packages:
        try:
            importlib.import_module(import_name)
            print(f"✅ {package_name} - متوفر")
        except ImportError:
            print(f"❌ {package_name} - مفقود")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n🔧 تثبيت المكتبات المفقودة...")
        for package in missing_packages:
            install_package(package)
    
    return len(missing_packages) == 0

def fix_pillow_issue():
    """إصلاح مشاكل Pillow الشائعة"""
    print("\n🔧 إصلاح مشاكل Pillow...")
    
    # إلغاء تثبيت Pillow القديم
    run_command(f"{sys.executable} -m pip uninstall Pillow -y")
    
    # تثبيت إصدار متوافق
    success = install_package("Pillow>=10.4.0")
    
    if not success:
        # محاولة تثبيت إصدار أقدم متوافق
        print("🔄 محاولة تثبيت إصدار أقدم من Pillow...")
        success = install_package("Pillow==9.5.0")
    
    return success

def test_imports():
    """اختبار استيراد المكتبات"""
    print("\n🧪 اختبار استيراد المكتبات...")
    
    imports = [
        ("tkinter", "tkinter"),
        ("ttkbootstrap", "ttkbootstrap"),
        ("PIL", "Pillow"),
        ("reportlab", "reportlab"),
        ("openpyxl", "openpyxl"),
        ("bidi", "python-bidi"),
        ("arabic_reshaper", "arabic-reshaper")
    ]
    
    failed_imports = []
    
    for module, package in imports:
        try:
            importlib.import_module(module)
            print(f"✅ {package} - يعمل")
        except ImportError as e:
            print(f"❌ {package} - لا يعمل: {e}")
            failed_imports.append(package)
    
    return len(failed_imports) == 0

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database import Database
        db = Database("test_fix.db")
        
        # اختبار بسيط
        result = db.authenticate_user('admin', 'admin123')
        if result:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            
            # حذف قاعدة البيانات التجريبية
            if os.path.exists("test_fix.db"):
                os.remove("test_fix.db")
            
            return True
        else:
            print("❌ مشكلة في قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_gui():
    """اختبار الواجهة الرسومية"""
    print("\n🖥️ اختبار الواجهة الرسومية...")
    
    try:
        import ttkbootstrap as ttk_bs
        
        # إنشاء نافذة تجريبية
        root = ttk_bs.Window()
        root.withdraw()
        root.destroy()
        
        print("✅ الواجهة الرسومية تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة الرسومية: {e}")
        return False

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    print("\n🔗 إنشاء اختصار على سطح المكتب...")
    
    try:
        import os
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        if os.path.exists(desktop):
            shortcut_content = f'''@echo off
cd /d "{os.getcwd()}"
python run.py
pause'''
            
            shortcut_path = os.path.join(desktop, "برنامج الحلاقة.bat")
            with open(shortcut_path, 'w', encoding='utf-8') as f:
                f.write(shortcut_content)
            
            print(f"✅ تم إنشاء اختصار: {shortcut_path}")
            return True
        else:
            print("⚠️  لم يتم العثور على سطح المكتب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختصار: {e}")
        return False

def main():
    """الدالة الرئيسية لإصلاح المشاكل"""
    print("=" * 60)
    print("🔧 أداة إصلاح مشاكل برنامج إدارة الحلاقة")
    print("=" * 60)
    
    steps = [
        ("فحص Python", check_python),
        ("ترقية pip", fix_pip),
        ("إصلاح مشاكل Pillow", fix_pillow_issue),
        ("فحص وتثبيت المكتبات", check_and_install_packages),
        ("اختبار الاستيراد", test_imports),
        ("اختبار قاعدة البيانات", test_database),
        ("اختبار الواجهة الرسومية", test_gui),
        ("إنشاء اختصار سطح المكتب", create_desktop_shortcut)
    ]
    
    passed = 0
    total = len(steps)
    
    for step_name, step_func in steps:
        try:
            if step_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في {step_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {passed}/{total} خطوة نجحت")
    
    if passed >= total - 1:  # السماح بفشل خطوة واحدة (مثل اختصار سطح المكتب)
        print("🎉 تم إصلاح جميع المشاكل! البرنامج جاهز للتشغيل.")
        print("\nيمكنك الآن تشغيل البرنامج باستخدام:")
        print("  python run.py")
        print("أو:")
        print("  install_and_run.bat")
    else:
        print("⚠️  لا تزال هناك بعض المشاكل. يرجى:")
        print("1. التأكد من اتصال الإنترنت")
        print("2. تشغيل الأمر كمدير")
        print("3. التواصل مع الدعم الفني")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
