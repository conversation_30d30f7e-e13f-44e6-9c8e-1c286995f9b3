#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ثيم عصري مخصص لبرنامج إدارة الحلاقة
يحتوي على ألوان وأنماط حديثة مع لمسة حلاقة احترافية
"""

import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs

class ModernBarberTheme:
    """فئة الثيم العصري للحلاقة"""
    
    def __init__(self):
        self.setup_colors()
        self.setup_fonts()
        self.setup_gradients()
    
    def setup_colors(self):
        """إعداد لوحة الألوان العصرية"""
        self.colors = {
            # ألوان أساسية مستوحاة من الحلاقة
            'barber_red': '#DC143C',        # أحمر الحلاقة الكلاسيكي
            'barber_blue': '#4169E1',       # أزرق الحلاقة
            'barber_white': '#F8F8FF',      # أبيض نظيف
            'barber_black': '#1C1C1C',      # أسود عميق
            
            # ألوان عصرية
            'primary': '#E74C3C',           # أحمر عصري
            'secondary': '#3498DB',         # أزرق عصري
            'accent': '#F39C12',            # ذهبي لامع
            'success': '#27AE60',           # أخضر النجاح
            'warning': '#F39C12',           # برتقالي التحذير
            'danger': '#E74C3C',            # أحمر الخطر
            'info': '#3498DB',              # أزرق المعلومات
            
            # ألوان الخلفية
            'bg_primary': '#2C3E50',        # خلفية داكنة رئيسية
            'bg_secondary': '#34495E',      # خلفية داكنة ثانوية
            'bg_light': '#ECF0F1',          # خلفية فاتحة
            'bg_card': '#FFFFFF',           # خلفية البطاقات
            'bg_surface': '#F8F9FA',        # خلفية السطح
            
            # ألوان النص
            'text_primary': '#2C3E50',      # نص رئيسي
            'text_secondary': '#7F8C8D',    # نص ثانوي
            'text_light': '#FFFFFF',        # نص فاتح
            'text_muted': '#95A5A6',        # نص خافت
            
            # ألوان الحدود
            'border_light': '#E0E0E0',      # حدود فاتحة
            'border_dark': '#BDC3C7',       # حدود داكنة
            'border_accent': '#F39C12',     # حدود مميزة
            
            # تدرجات
            'gradient_primary': ['#E74C3C', '#C0392B'],
            'gradient_secondary': ['#3498DB', '#2980B9'],
            'gradient_dark': ['#2C3E50', '#1A252F'],
        }
    
    def setup_fonts(self):
        """إعداد الخطوط العصرية"""
        self.fonts = {
            # خطوط العناوين
            'title_large': ('Segoe UI', 28, 'bold'),
            'title_medium': ('Segoe UI', 24, 'bold'),
            'title_small': ('Segoe UI', 20, 'bold'),
            
            # خطوط العناوين الفرعية
            'subtitle_large': ('Segoe UI', 18, 'bold'),
            'subtitle_medium': ('Segoe UI', 16, 'bold'),
            'subtitle_small': ('Segoe UI', 14, 'bold'),
            
            # خطوط النص
            'body_large': ('Segoe UI', 14),
            'body_medium': ('Segoe UI', 12),
            'body_small': ('Segoe UI', 10),
            
            # خطوط الأزرار
            'button_large': ('Segoe UI', 14, 'bold'),
            'button_medium': ('Segoe UI', 12, 'bold'),
            'button_small': ('Segoe UI', 10, 'bold'),
            
            # خطوط خاصة
            'monospace': ('Consolas', 12),
            'icon': ('Segoe UI Emoji', 16),
            'icon_large': ('Segoe UI Emoji', 24),
        }
    
    def setup_gradients(self):
        """إعداد التدرجات اللونية"""
        self.gradients = {
            'header': {
                'colors': ['#E74C3C', '#C0392B'],
                'direction': 'horizontal'
            },
            'sidebar': {
                'colors': ['#2C3E50', '#1A252F'],
                'direction': 'vertical'
            },
            'card': {
                'colors': ['#FFFFFF', '#F8F9FA'],
                'direction': 'vertical'
            },
            'button_primary': {
                'colors': ['#E74C3C', '#C0392B'],
                'direction': 'vertical'
            },
            'button_secondary': {
                'colors': ['#3498DB', '#2980B9'],
                'direction': 'vertical'
            }
        }
    
    def get_barber_icons(self):
        """الحصول على أيقونات الحلاقة"""
        return {
            # أدوات الحلاقة
            'scissors': '✂️',
            'razor': '🪒',
            'comb': '🪮',
            'brush': '🖌️',
            'mirror': '🪞',
            'towel': '🏳️',
            'bottle': '🧴',
            'spray': '💨',
            
            # أثاث الحلاقة
            'chair': '💺',
            'pole': '💈',
            'sink': '🚿',
            'cabinet': '🗄️',
            
            # أشخاص
            'barber': '👨‍🦲',
            'customer': '👤',
            'customers': '👥',
            'man': '👨',
            'woman': '👩',
            
            # عمليات
            'cut': '✂️',
            'wash': '🧼',
            'style': '💇',
            'shave': '🪒',
            'trim': '✂️',
            
            # إدارة
            'money': '💰',
            'cash': '💵',
            'card': '💳',
            'receipt': '🧾',
            'calendar': '📅',
            'clock': '🕐',
            'appointment': '📋',
            
            # تنقل
            'home': '🏠',
            'dashboard': '📊',
            'reports': '📈',
            'settings': '⚙️',
            'logout': '🚪',
            'login': '🔑',
            
            # حالات
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️',
            'loading': '⏳',
            
            # أدوات واجهة
            'search': '🔍',
            'filter': '🔽',
            'sort': '🔄',
            'add': '➕',
            'edit': '✏️',
            'delete': '🗑️',
            'save': '💾',
            'cancel': '❌',
            'refresh': '🔄',
            'print': '🖨️',
            'export': '📤',
            'import': '📥',
            
            # لغات
            'arabic': '🇸🇦',
            'french': '🇫🇷',
            'english': '🇺🇸',
            
            # متنوعة
            'star': '⭐',
            'heart': '❤️',
            'thumbs_up': '👍',
            'fire': '🔥',
            'crown': '👑',
            'diamond': '💎',
        }
    
    def apply_modern_style(self, style_obj):
        """تطبيق الأنماط العصرية"""
        
        # أنماط الإطارات
        style_obj.configure('Modern.TFrame',
                           background=self.colors['bg_card'],
                           borderwidth=0,
                           relief='flat')
        
        style_obj.configure('Card.TFrame',
                           background=self.colors['bg_card'],
                           borderwidth=1,
                           relief='solid',
                           bordercolor=self.colors['border_light'])
        
        style_obj.configure('Header.TFrame',
                           background=self.colors['primary'],
                           borderwidth=0,
                           relief='flat')
        
        # أنماط التسميات
        style_obj.configure('ModernTitle.TLabel',
                           font=self.fonts['title_large'],
                           foreground=self.colors['primary'],
                           background=self.colors['bg_card'])
        
        style_obj.configure('ModernSubtitle.TLabel',
                           font=self.fonts['subtitle_large'],
                           foreground=self.colors['text_primary'],
                           background=self.colors['bg_card'])
        
        style_obj.configure('ModernBody.TLabel',
                           font=self.fonts['body_medium'],
                           foreground=self.colors['text_primary'],
                           background=self.colors['bg_card'])
        
        # أنماط الأزرار
        style_obj.configure('ModernPrimary.TButton',
                           font=self.fonts['button_medium'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(20, 10))
        
        style_obj.configure('ModernSecondary.TButton',
                           font=self.fonts['button_medium'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(20, 10))
        
        # أنماط الجداول
        style_obj.configure('Modern.Treeview',
                           background=self.colors['bg_card'],
                           foreground=self.colors['text_primary'],
                           fieldbackground=self.colors['bg_card'],
                           borderwidth=1,
                           relief='solid')
        
        style_obj.configure('Modern.Treeview.Heading',
                           font=self.fonts['subtitle_small'],
                           background=self.colors['bg_secondary'],
                           foreground=self.colors['text_light'],
                           borderwidth=1,
                           relief='solid')
        
        # أنماط حقول الإدخال
        style_obj.configure('Modern.TEntry',
                           font=self.fonts['body_medium'],
                           borderwidth=2,
                           relief='solid',
                           bordercolor=self.colors['border_light'],
                           focuscolor=self.colors['primary'])
        
        return style_obj
    
    def create_gradient_frame(self, parent, gradient_name, width=None, height=None):
        """إنشاء إطار بتدرج لوني"""
        gradient = self.gradients.get(gradient_name, self.gradients['card'])
        
        # هذه دالة مبسطة - في التطبيق الحقيقي نحتاج مكتبة إضافية للتدرجات
        frame = ttk_bs.Frame(parent, style='Modern.TFrame')
        if width:
            frame.configure(width=width)
        if height:
            frame.configure(height=height)
        
        return frame

# إنشاء مثيل عام للثيم
modern_theme = ModernBarberTheme()
