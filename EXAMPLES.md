# أمثلة الاستخدام - برنامج إدارة الحلاقة

هذا الملف يحتوي على أمثلة عملية لاستخدام برنامج إدارة الحلاقة في سيناريوهات مختلفة.

## 🚀 البدء السريع

### 1. التشغيل لأول مرة

```bash
# تشغيل ملف الإعداد الشامل
setup_and_run.bat

# أو تشغيل الخطوات يدوياً:
# 1. تثبيت المكتبات
pip install -r requirements.txt

# 2. فحص التثبيت
python check_installation.py

# 3. تشغيل البرنامج
python run.py
```

### 2. تسجيل الدخول الأول

**للمدير:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

**للعاملين:**
- اسم المستخدم: أي اسم (مثل: `أحمد`, `محمد`, `علي`)
- كلمة المرور: غير مطلوبة

## 📋 سيناريوهات الاستخدام

### السيناريو الأول: إعداد صالون جديد

#### الخطوة 1: إضافة العاملين
1. سجل دخول كمدير
2. اطلب من كل عامل تسجيل الدخول بالاسم فقط
3. سيتم إضافتهم تلقائياً إلى النظام

#### الخطوة 2: إضافة الخدمات (المدير فقط)
```
الخدمات الافتراضية موجودة:
- قص شعر عادي: 50 ج.م
- قص شعر + حلاقة: 80 ج.م
- حلاقة فقط: 30 ج.م
- تصفيف شعر: 40 ج.م
- صبغة شعر: 150 ج.م

يمكن إضافة خدمات جديدة:
- قص شعر أطفال: 30 ج.م
- حلاقة ذقن: 25 ج.م
- غسيل شعر: 20 ج.م
```

#### الخطوة 3: إضافة الزبائن
كل عامل يضيف زبائنه:
```
مثال:
- الاسم: أحمد محمد علي
- الهاتف: 01234567890
- الصورة: اختيارية
- أضافه: اسم العامل
```

### السيناريو الثاني: يوم عمل عادي

#### صباح اليوم:
1. **العامل أحمد** يسجل دخول
2. يضيف زبون جديد: "خالد أحمد"
3. ينفذ خدمة "قص شعر عادي" للزبون
4. النظام يحسب: 50 ج.م للعامل (100% لأنه صاحب الزبون)

#### ظهر اليوم:
1. **العامل محمد** يسجل دخول
2. ينفذ خدمة "حلاقة ذقن" لزبون العامل أحمد
3. النظام يحسب: 12.5 ج.م للعامل محمد، 12.5 ج.م للعامل أحمد

#### مساء اليوم:
1. **المدير** يراجع التقارير اليومية
2. يرى إجمالي العمليات والأرباح
3. يصدر تقرير PDF للمراجعة

### السيناريو الثالث: نهاية الشهر

#### مراجعة الأرباح:
```
تقرير شهري للعامل أحمد:
- عدد العمليات: 45
- إجمالي الإيرادات: 2,250 ج.م
- أرباح العامل: 1,800 ج.م
- أرباح المالك: 450 ج.م
```

#### تصدير التقارير:
1. المدير يختار التاريخ من بداية الشهر لنهايته
2. يختار "جميع المستخدمين" أو مستخدم محدد
3. يصدر التقرير إلى Excel للمحاسبة

## 🔧 حالات خاصة

### إضافة خدمة جديدة
```python
# المدير فقط يمكنه إضافة خدمات
اسم الخدمة: "تسريح عروس"
السعر: 200.00
```

### تعديل نسب الأرباح
```python
# في ملف config.py
"employee_share_percentage": 60,  # 60% للعامل
"owner_share_percentage": 40,     # 40% للمالك
```

### تغيير اللغة
```python
# أثناء التشغيل: زر "اللغة" في الواجهة
# أو في ملف config.py:
"language": "fr"  # للفرنسية
```

## 📊 أمثلة التقارير

### تقرير يومي
```
التاريخ: 2025-01-22
العامل: أحمد

العمليات:
1. خالد أحمد - قص شعر عادي - 50 ج.م
2. محمد علي - حلاقة ذقن - 25 ج.م
3. سامي حسن - صبغة شعر - 150 ج.م

الإجمالي: 225 ج.م
نصيب العامل: 200 ج.م
نصيب المالك: 25 ج.م
```

### تقرير شهري شامل
```
الشهر: يناير 2025

إحصائيات عامة:
- إجمالي العمليات: 180
- إجمالي الإيرادات: 9,500 ج.م
- أرباح العاملين: 7,200 ج.م
- أرباح المالك: 2,300 ج.م

أداء العاملين:
1. أحمد: 60 عملية - 3,200 ج.م
2. محمد: 45 عملية - 2,100 ج.م
3. علي: 35 عملية - 1,900 ج.م
```

## 🛠️ نصائح للاستخدام الأمثل

### للمدير:
1. راجع التقارير اليومية لمتابعة الأداء
2. صدر تقارير شهرية للمحاسبة
3. أضف خدمات جديدة حسب الطلب
4. احتفظ بنسخ احتياطية من قاعدة البيانات

### للعاملين:
1. أضف زبائنك الجدد فور وصولهم
2. سجل العمليات فور انتهائها
3. تأكد من اختيار الخدمة والسعر الصحيح
4. راجع أرباحك في التقارير

### للصيانة:
1. شغل `check_installation.py` دورياً
2. احتفظ بنسخة من مجلد البرنامج كاملاً
3. تأكد من تحديث المكتبات عند الحاجة

## 🚨 حل المشاكل الشائعة

### مشكلة: البرنامج لا يبدأ
```bash
# الحل:
python check_installation.py
# ثم اتبع التعليمات
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# الحل:
# احذف ملف coiffeur.db
# شغل البرنامج مرة أخرى لإنشاء قاعدة بيانات جديدة
```

### مشكلة: مشاكل في الترجمة
```bash
# الحل:
pip install python-bidi arabic-reshaper
```

### مشكلة: لا يمكن تصدير PDF
```bash
# الحل:
pip install reportlab
```

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع ملف `README.md`
2. شغل `check_installation.py`
3. راجع رسائل الخطأ في وحدة التحكم
4. تواصل مع الدعم الفني

---

**نتمنى لك تجربة ممتعة مع برنامج إدارة الحلاقة!** 💈
