@echo off
chcp 65001 >nul
title إعداد وتشغيل برنامج إدارة الحلاقة
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██              برنامج إدارة الحلاقة                         ██
echo ██                  الإصدار 1.0.0                           ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:MENU
echo اختر العملية المطلوبة:
echo.
echo [1] فحص التثبيت
echo [2] تثبيت المكتبات المطلوبة
echo [3] تشغيل البرنامج
echo [4] اختبارات مبسطة
echo [5] اختبارات شاملة
echo [6] بناء ملف EXE
echo [7] خروج
echo.
set /p choice="أدخل اختيارك (1-7): "

if "%choice%"=="1" goto CHECK
if "%choice%"=="2" goto INSTALL
if "%choice%"=="3" goto RUN
if "%choice%"=="4" goto SIMPLE_TEST
if "%choice%"=="5" goto FULL_TEST
if "%choice%"=="6" goto BUILD
if "%choice%"=="7" goto EXIT

echo اختيار غير صحيح، يرجى المحاولة مرة أخرى.
pause
cls
goto MENU

:CHECK
echo.
echo ═══════════════════════════════════════
echo           فحص سلامة التثبيت
echo ═══════════════════════════════════════
echo.
python check_installation.py
echo.
pause
cls
goto MENU

:INSTALL
echo.
echo ═══════════════════════════════════════
echo         تثبيت المكتبات المطلوبة
echo ═══════════════════════════════════════
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من python.org
    pause
    goto MENU
)

echo تثبيت المكتبات...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت بعض المكتبات
    echo يرجى التحقق من اتصال الإنترنت ومحاولة مرة أخرى
) else (
    echo ✅ تم تثبيت جميع المكتبات بنجاح
)

echo.
pause
cls
goto MENU

:RUN
echo.
echo ═══════════════════════════════════════
echo            تشغيل البرنامج
echo ═══════════════════════════════════════
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    pause
    goto MENU
)

echo تشغيل برنامج إدارة الحلاقة...
echo.
python run.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo تأكد من تثبيت جميع المكتبات المطلوبة
    echo.
)

pause
cls
goto MENU

:SIMPLE_TEST
echo.
echo ═══════════════════════════════════════
echo          اختبارات مبسطة
echo ═══════════════════════════════════════
echo.

python simple_test.py

echo.
pause
cls
goto MENU

:FULL_TEST
echo.
echo ═══════════════════════════════════════
echo          اختبارات شاملة
echo ═══════════════════════════════════════
echo.

python test_app.py

echo.
pause
cls
goto MENU

:BUILD
echo.
echo ═══════════════════════════════════════
echo            بناء ملف EXE
echo ═══════════════════════════════════════
echo.

echo هذه العملية قد تستغرق عدة دقائق...
echo.

python build_exe.py

if errorlevel 1 (
    echo ❌ فشل في بناء ملف EXE
    echo تأكد من تثبيت PyInstaller
) else (
    echo ✅ تم بناء ملف EXE بنجاح
    echo الملف متوفر في مجلد dist/
)

echo.
pause
cls
goto MENU

:EXIT
echo.
echo شكراً لاستخدام برنامج إدارة الحلاقة!
echo.
timeout /t 2 >nul
exit

:ERROR
echo حدث خطأ غير متوقع
pause
goto MENU
