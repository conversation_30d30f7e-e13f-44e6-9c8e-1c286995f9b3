#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البرنامج مع عرض رسائل التشخيص
"""

import sys
import traceback

def check_all_imports():
    """فحص جميع الاستيرادات مع عرض التفاصيل"""
    print("🔍 فحص الاستيرادات...")
    
    imports_to_check = [
        ("tkinter", "واجهة المستخدم الأساسية"),
        ("tkinter.ttk", "عناصر الواجهة المحسنة"),
        ("tkinter.messagebox", "صناديق الرسائل"),
        ("tkinter.filedialog", "حوارات الملفات"),
        ("ttkbootstrap", "الواجهة الحديثة"),
        ("PIL", "معالجة الصور"),
        ("sqlite3", "قاعدة البيانات"),
        ("reportlab", "تصدير PDF"),
        ("openpyxl", "تصدير Excel"),
        ("bidi", "دعم النصوص العربية"),
        ("arabic_reshaper", "تنسيق النصوص العربية"),
        ("os", "نظام التشغيل"),
        ("datetime", "التاريخ والوقت"),
        ("json", "معالجة JSON")
    ]
    
    missing_imports = []
    
    for module_name, description in imports_to_check:
        try:
            __import__(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError as e:
            print(f"❌ {module_name} - {description} - خطأ: {e}")
            missing_imports.append((module_name, str(e)))
        except Exception as e:
            print(f"⚠️  {module_name} - {description} - تحذير: {e}")
    
    return missing_imports

def check_project_files():
    """فحص ملفات المشروع"""
    print("\n🔍 فحص ملفات المشروع...")
    
    required_files = [
        ("main.py", "الواجهة الرئيسية"),
        ("database.py", "قاعدة البيانات"),
        ("translations.py", "نظام الترجمة"),
        ("reports.py", "نظام التقارير"),
        ("config.py", "الإعدادات"),
        ("version.py", "معلومات الإصدار")
    ]
    
    missing_files = []
    
    import os
    for filename, description in required_files:
        if os.path.exists(filename):
            print(f"✅ {filename} - {description}")
        else:
            print(f"❌ {filename} - {description} - مفقود")
            missing_files.append(filename)
    
    return missing_files

def test_project_imports():
    """اختبار استيراد ملفات المشروع"""
    print("\n🔍 اختبار استيراد ملفات المشروع...")
    
    project_modules = [
        ("database", "Database"),
        ("translations", "Translator"),
        ("config", "app_config"),
        ("version", "__version__")
    ]
    
    import_errors = []
    
    for module_name, class_or_var in project_modules:
        try:
            module = __import__(module_name)
            if hasattr(module, class_or_var):
                print(f"✅ {module_name}.{class_or_var}")
            else:
                print(f"⚠️  {module_name}.{class_or_var} - غير موجود")
                import_errors.append(f"{module_name}.{class_or_var}")
        except ImportError as e:
            print(f"❌ {module_name} - خطأ استيراد: {e}")
            import_errors.append(f"{module_name}: {e}")
        except Exception as e:
            print(f"⚠️  {module_name} - خطأ: {e}")
            import_errors.append(f"{module_name}: {e}")
    
    return import_errors

def run_main_app():
    """تشغيل التطبيق الرئيسي مع معالجة الأخطاء"""
    print("\n🚀 تشغيل التطبيق الرئيسي...")
    
    try:
        # استيراد التطبيق الرئيسي
        from main import CoiffeurApp
        print("✅ تم استيراد التطبيق الرئيسي بنجاح")
        
        # إنشاء وتشغيل التطبيق
        print("🔄 إنشاء التطبيق...")
        app = CoiffeurApp()
        print("✅ تم إنشاء التطبيق بنجاح")
        
        print("🔄 تشغيل التطبيق...")
        app.run()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        return False
    
    return True

def main():
    """الدالة الرئيسية للتشخيص والتشغيل"""
    print("=" * 60)
    print("🔧 تشخيص وتشغيل برنامج إدارة الحلاقة")
    print("=" * 60)
    
    # فحص الاستيرادات
    missing_imports = check_all_imports()
    
    # فحص ملفات المشروع
    missing_files = check_project_files()
    
    # اختبار استيراد ملفات المشروع
    import_errors = test_project_imports()
    
    # عرض ملخص المشاكل
    total_issues = len(missing_imports) + len(missing_files) + len(import_errors)
    
    print(f"\n📊 ملخص الفحص:")
    print(f"   - مكتبات مفقودة: {len(missing_imports)}")
    print(f"   - ملفات مفقودة: {len(missing_files)}")
    print(f"   - أخطاء استيراد: {len(import_errors)}")
    print(f"   - إجمالي المشاكل: {total_issues}")
    
    if total_issues > 0:
        print("\n⚠️  تم العثور على مشاكل:")
        
        if missing_imports:
            print("\n❌ مكتبات مفقودة:")
            for module, error in missing_imports:
                print(f"   - {module}: {error}")
            print("\n💡 لحل مشاكل المكتبات، شغل:")
            print("   pip install ttkbootstrap Pillow reportlab openpyxl python-bidi arabic-reshaper")
        
        if missing_files:
            print("\n❌ ملفات مفقودة:")
            for filename in missing_files:
                print(f"   - {filename}")
        
        if import_errors:
            print("\n❌ أخطاء استيراد:")
            for error in import_errors:
                print(f"   - {error}")
        
        print("\n🔧 يرجى حل هذه المشاكل قبل تشغيل البرنامج")
        return False
    
    else:
        print("\n✅ لا توجد مشاكل! محاولة تشغيل البرنامج...")
        return run_main_app()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل في تشغيل البرنامج")
            input("اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        input("اضغط Enter للخروج...")
