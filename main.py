import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from PIL import Image, ImageTk
import os
from datetime import datetime, date
import webbrowser

from database import Database
from translations import Translator
from config import app_config
from modern_theme import modern_theme
from employee_cards_styles import employee_card_styles

class CoiffeurApp:
    def __init__(self):
        self.db = Database(app_config.get_database_path())
        self.translator = Translator(app_config.get_language())
        self.current_user = None
        self.current_role = None

        # تحميل الألوان والخطوط أولاً
        self.colors = modern_theme.colors
        self.fonts = modern_theme.fonts
        self.barber_icons = modern_theme.get_barber_icons()

        # إنشاء النافذة الرئيسية مع ثيم عصري
        self.root = ttk_bs.Window(themename="superhero")  # ثيم داكن عصري
        self.root.title("💈 " + self.translator.get('login_title'))
        self.root.geometry("1400x900")
        self.root.resizable(True, True)

        # تعيين الأيقونة
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # تعيين خلفية عصرية
        self.root.configure(bg=self.colors['bg_primary'])

        # متغيرات الواجهة
        self.setup_modern_styles()
        self.show_login_screen()
        
    def setup_modern_styles(self):
        """إعداد الأنماط العصرية مع لمسة حلاقة"""
        style = ttk_bs.Style()

        # تطبيق الأنماط العصرية
        modern_theme.apply_modern_style(style)
        
    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول بنظام البطاقات"""
        # مسح المحتوى الحالي
        for widget in self.root.winfo_children():
            widget.destroy()

        # خلفية عصرية
        main_canvas = tk.Canvas(self.root, bg=self.colors['bg_primary'], highlightthickness=0)
        main_canvas.pack(fill='both', expand=True)

        # إطار رئيسي قابل للتمرير
        main_frame = ttk_bs.Frame(main_canvas, style='Card.TFrame')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # شريط العنوان
        header_frame = ttk_bs.Frame(main_frame, bootstyle="danger")
        header_frame.pack(fill='x', pady=(0, 20))

        # عنوان الصالون
        title_container = ttk_bs.Frame(header_frame, bootstyle="danger")
        title_container.pack(expand=True, fill='both', pady=15)

        # أيقونة الحلاقة
        barber_icon = ttk_bs.Label(
            title_container,
            text="💈",
            font=('Segoe UI Emoji', 36),
            bootstyle="inverse-danger"
        )
        barber_icon.pack(pady=(5, 0))

        # العنوان
        title_label = ttk_bs.Label(
            title_container,
            text="صالون الحلاقة العصري",
            font=self.fonts['title_medium'],
            bootstyle="inverse-danger"
        )
        title_label.pack(pady=2)

        # العنوان الفرعي
        subtitle_label = ttk_bs.Label(
            title_container,
            text="اختر العامل لتسجيل الدخول",
            font=self.fonts['subtitle_medium'],
            bootstyle="inverse-danger"
        )
        subtitle_label.pack(pady=(0, 5))
        
        # إطار المحتوى الرئيسي
        content_frame = ttk_bs.Frame(main_frame, style='Card.TFrame')
        content_frame.pack(expand=True, fill='both', padx=20, pady=10)

        # اختيار اللغة
        lang_frame = ttk_bs.Frame(content_frame)
        lang_frame.pack(fill='x', pady=(0, 15))

        lang_label = ttk_bs.Label(
            lang_frame,
            text="🌐 اختر اللغة:",
            font=self.fonts['body_medium'],
            bootstyle="secondary"
        )
        lang_label.pack(side='left', padx=(0, 10))

        self.language_var = tk.StringVar(value='ar')

        ar_btn = ttk_bs.Button(
            lang_frame,
            text="🇸🇦 عربي",
            command=lambda: self.set_language('ar'),
            bootstyle="outline-secondary",
            width=8
        )
        ar_btn.pack(side='left', padx=2)

        fr_btn = ttk_bs.Button(
            lang_frame,
            text="🇫🇷 فرنسي",
            command=lambda: self.set_language('fr'),
            bootstyle="outline-secondary",
            width=8
        )
        fr_btn.pack(side='left', padx=2)

        # بطاقة المدير
        admin_section = ttk_bs.LabelFrame(
            content_frame,
            text="👨‍💼 تسجيل دخول المدير",
            bootstyle="success",
            padding=15
        )
        admin_section.pack(fill='x', pady=(0, 15))

        # حقول المدير
        admin_fields = ttk_bs.Frame(admin_section)
        admin_fields.pack(fill='x')

        # اسم المستخدم للمدير
        admin_user_frame = ttk_bs.Frame(admin_fields)
        admin_user_frame.pack(fill='x', pady=5)

        ttk_bs.Label(admin_user_frame, text="👤 اسم المستخدم:",
                    font=self.fonts['body_medium']).pack(side='left', padx=(0, 10))
        self.admin_username_entry = ttk_bs.Entry(admin_user_frame, font=self.fonts['body_medium'], width=15)
        self.admin_username_entry.pack(side='left', padx=5)
        self.admin_username_entry.insert(0, "admin")

        # كلمة المرور للمدير
        admin_pass_frame = ttk_bs.Frame(admin_fields)
        admin_pass_frame.pack(fill='x', pady=5)

        ttk_bs.Label(admin_pass_frame, text="🔒 كلمة المرور:",
                    font=self.fonts['body_medium']).pack(side='left', padx=(0, 10))
        self.admin_password_entry = ttk_bs.Entry(admin_pass_frame, font=self.fonts['body_medium'],
                                               show='*', width=15)
        self.admin_password_entry.pack(side='left', padx=5)

        # زر دخول المدير
        admin_login_btn = ttk_bs.Button(
            admin_section,
            text="🔑 دخول المدير",
            command=self.admin_login,
            bootstyle="success",
            width=15
        )
        admin_login_btn.pack(pady=(10, 0))

        # قسم العاملين
        employees_section = ttk_bs.LabelFrame(
            content_frame,
            text="👷‍♂️ اختر العامل لتسجيل الدخول",
            bootstyle="info",
            padding=15
        )
        employees_section.pack(fill='both', expand=True, pady=(0, 10))

        # إطار قابل للتمرير للعاملين
        employees_canvas = tk.Canvas(employees_section, bg=self.colors['bg_card'], highlightthickness=0)
        employees_scrollbar = ttk_bs.Scrollbar(employees_section, orient="vertical", command=employees_canvas.yview)
        employees_scrollable_frame = ttk_bs.Frame(employees_canvas)

        employees_scrollable_frame.bind(
            "<Configure>",
            lambda e: employees_canvas.configure(scrollregion=employees_canvas.bbox("all"))
        )

        employees_canvas.create_window((0, 0), window=employees_scrollable_frame, anchor="nw")
        employees_canvas.configure(yscrollcommand=employees_scrollbar.set)

        # تخطيط العاملين
        employees_canvas.pack(side="left", fill="both", expand=True)
        employees_scrollbar.pack(side="right", fill="y")

        # تحميل وعرض بطاقات العاملين
        self.load_employee_cards(employees_scrollable_frame)

        # زر إضافة عامل جديد
        add_employee_frame = ttk_bs.Frame(content_frame)
        add_employee_frame.pack(fill='x', pady=(10, 0))

        add_employee_btn = ttk_bs.Button(
            add_employee_frame,
            text="➕ إضافة عامل جديد",
            command=self.show_add_employee_dialog,
            bootstyle="outline-success",
            width=20
        )
        add_employee_btn.pack(pady=5)

        # ربط Enter بتسجيل دخول المدير
        self.root.bind('<Return>', lambda e: self.admin_login())

        # تركيز على حقل كلمة مرور المدير
        self.admin_password_entry.focus()
        
    def load_employee_cards(self, parent_frame):
        """تحميل وعرض بطاقات العاملين"""
        try:
            # الحصول على قائمة المستخدمين
            users = self.db.get_users()

            # تصفية العاملين فقط (استبعاد المدير)
            employees = [user for user in users if user[1] == 'employee']

            # إضافة بعض العاملين الافتراضيين إذا لم يوجد أي عامل
            if not employees:
                default_employees = [
                    ("أحمد محمد", "employee"),
                    ("محمد علي", "employee"),
                    ("علي حسن", "employee"),
                    ("حسن أحمد", "employee"),
                    ("سامي محمود", "employee"),
                    ("محمود سامي", "employee")
                ]

                # إضافة العاملين الافتراضيين إلى قاعدة البيانات
                for name, role in default_employees:
                    try:
                        self.db.add_user(name, role=role)
                    except:
                        pass  # العامل موجود بالفعل

                # إعادة تحميل القائمة
                users = self.db.get_users()
                employees = [user for user in users if user[1] == 'employee']

            # إنشاء بطاقات العاملين
            self.create_employee_cards(parent_frame, employees)

        except Exception as e:
            # في حالة الخطأ، عرض رسالة
            error_label = ttk_bs.Label(
                parent_frame,
                text=f"خطأ في تحميل العاملين: {str(e)}",
                bootstyle="danger"
            )
            error_label.pack(pady=20)

    def create_employee_cards(self, parent_frame, employees):
        """إنشاء بطاقات العاملين"""
        # تنظيم البطاقات في صفوف (3 بطاقات في كل صف)
        cards_per_row = 3
        current_row_frame = None

        for i, (username, role) in enumerate(employees):
            # إنشاء صف جديد كل 3 بطاقات
            if i % cards_per_row == 0:
                current_row_frame = ttk_bs.Frame(parent_frame)
                current_row_frame.pack(fill='x', pady=5)

            # إنشاء بطاقة العامل
            self.create_single_employee_card(current_row_frame, username)

    def create_single_employee_card(self, parent_frame, username):
        """إنشاء بطاقة عامل واحد محسنة"""
        # الحصول على بيانات البطاقة المحسنة
        card_data = employee_card_styles.create_enhanced_card_data(username)

        # إطار البطاقة الخارجي
        card_outer_frame = ttk_bs.Frame(parent_frame)
        card_outer_frame.pack(side='left', padx=8, pady=8, fill='both', expand=True)

        # البطاقة الرئيسية
        card_content = ttk_bs.LabelFrame(
            card_outer_frame,
            text="",
            bootstyle=card_data["style"],
            padding=18
        )
        card_content.pack(fill='both', expand=True)

        # أيقونة العامل المخصصة
        employee_icon = ttk_bs.Label(
            card_content,
            text=card_data["avatar"],
            font=('Segoe UI Emoji', 36),
            bootstyle=f"inverse-{card_data['style']}"
        )
        employee_icon.pack(pady=(0, 8))

        # اسم العامل
        name_label = ttk_bs.Label(
            card_content,
            text=username,
            font=self.fonts['subtitle_small'],
            bootstyle=f"inverse-{card_data['style']}"
        )
        name_label.pack(pady=(0, 5))

        # تخصص العامل
        specialty_label = ttk_bs.Label(
            card_content,
            text=card_data["specialty"],
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{card_data['style']}"
        )
        specialty_label.pack(pady=(0, 8))

        # إحصائيات مبسطة
        stats_frame = ttk_bs.Frame(card_content, bootstyle=card_data["style"])
        stats_frame.pack(fill='x', pady=(0, 8))

        # تقييم العامل
        rating_label = ttk_bs.Label(
            stats_frame,
            text=f"⭐ {card_data['stats']['rating']}",
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{card_data['style']}"
        )
        rating_label.pack(side='left')

        # عدد الزبائن
        customers_label = ttk_bs.Label(
            stats_frame,
            text=f"👥 {card_data['stats']['total_customers']}",
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{card_data['style']}"
        )
        customers_label.pack(side='right')

        # شارات العامل
        if card_data["badges"]:
            badges_frame = ttk_bs.Frame(card_content, bootstyle=card_data["style"])
            badges_frame.pack(fill='x', pady=(0, 8))

            for badge in card_data["badges"][:2]:  # عرض أول شارتين فقط
                badge_label = ttk_bs.Label(
                    badges_frame,
                    text=f"{badge['icon']} {badge['text']}",
                    font=self.fonts['body_small'],
                    bootstyle=f"inverse-{card_data['style']}"
                )
                badge_label.pack(side='left', padx=2)

        # زر تسجيل الدخول المحسن
        login_btn = ttk_bs.Button(
            card_content,
            text="🔑 دخول",
            command=lambda: self.employee_card_login(username),
            bootstyle="light",
            width=14
        )
        login_btn.pack(pady=(5, 0), ipady=5)

        # تأثيرات التمرير المحسنة
        self.add_enhanced_card_hover_effect(card_content, card_data["style"])

    def add_enhanced_card_hover_effect(self, card, original_style):
        """إضافة تأثير التمرير المحسن للبطاقة"""
        # تحديد الأنماط للتمرير
        hover_styles = {
            "primary": "secondary",
            "secondary": "success",
            "success": "info",
            "info": "warning",
            "warning": "danger",
            "danger": "primary"
        }

        hover_style = hover_styles.get(original_style, "secondary")

        def on_enter(event):
            card.configure(bootstyle=hover_style)
            # تحديث جميع العناصر الفرعية
            self.update_child_styles(card, f"inverse-{hover_style}")

        def on_leave(event):
            card.configure(bootstyle=original_style)
            # إعادة تعيين العناصر الفرعية
            self.update_child_styles(card, f"inverse-{original_style}")

        card.bind("<Enter>", on_enter)
        card.bind("<Leave>", on_leave)

        # تطبيق التأثير على جميع العناصر الفرعية
        for child in card.winfo_children():
            child.bind("<Enter>", on_enter)
            child.bind("<Leave>", on_leave)

    def update_child_styles(self, parent, new_style):
        """تحديث أنماط العناصر الفرعية"""
        for child in parent.winfo_children():
            try:
                if hasattr(child, 'configure') and 'bootstyle' in child.configure():
                    child.configure(bootstyle=new_style)
            except:
                pass
            # تحديث العناصر الفرعية للعناصر الفرعية
            self.update_child_styles(child, new_style)

    def show_add_employee_dialog(self):
        """عرض حوار إضافة عامل جديد"""
        dialog = ttk_bs.Toplevel(self.root)
        dialog.title("إضافة عامل جديد")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.grab_set()  # جعل النافذة modal

        # توسيط النافذة
        dialog.transient(self.root)

        # إطار المحتوى
        content_frame = ttk_bs.Frame(dialog, padding=20)
        content_frame.pack(fill='both', expand=True)

        # العنوان
        title_label = ttk_bs.Label(
            content_frame,
            text="👷‍♂️ إضافة عامل جديد",
            font=self.fonts['title_small'],
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # حقل اسم العامل
        name_frame = ttk_bs.Frame(content_frame)
        name_frame.pack(fill='x', pady=10)

        ttk_bs.Label(name_frame, text="اسم العامل:",
                    font=self.fonts['body_medium']).pack(anchor='w', pady=(0, 5))
        name_entry = ttk_bs.Entry(name_frame, font=self.fonts['body_medium'], width=30)
        name_entry.pack(fill='x', ipady=5)
        name_entry.focus()

        # معلومات إضافية
        info_label = ttk_bs.Label(
            content_frame,
            text="💡 سيتم إنشاء بطاقة جديدة للعامل تلقائياً",
            font=self.fonts['body_small'],
            bootstyle="secondary"
        )
        info_label.pack(pady=10)

        # أزرار الحوار
        buttons_frame = ttk_bs.Frame(content_frame)
        buttons_frame.pack(fill='x', pady=(20, 0))

        # زر الإضافة
        add_btn = ttk_bs.Button(
            buttons_frame,
            text="✅ إضافة",
            command=lambda: self.add_new_employee(dialog, name_entry.get().strip()),
            bootstyle="success",
            width=12
        )
        add_btn.pack(side='left', padx=(0, 10))

        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            bootstyle="secondary",
            width=12
        )
        cancel_btn.pack(side='left')

        # ربط Enter بالإضافة
        dialog.bind('<Return>', lambda e: self.add_new_employee(dialog, name_entry.get().strip()))

    def add_new_employee(self, dialog, employee_name):
        """إضافة عامل جديد"""
        if not employee_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم العامل")
            return

        try:
            # إضافة العامل إلى قاعدة البيانات
            if self.db.add_user(employee_name, role='employee'):
                messagebox.showinfo("نجح", f"تم إضافة العامل '{employee_name}' بنجاح!")
                dialog.destroy()
                # إعادة تحميل شاشة تسجيل الدخول لإظهار العامل الجديد
                self.show_login_screen()
            else:
                messagebox.showerror("خطأ", "العامل موجود بالفعل أو حدث خطأ في الإضافة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def employee_card_login(self, username):
        """تسجيل دخول العامل من البطاقة"""
        self.current_user = username
        self.current_role = 'employee'

        # إضافة العامل إلى قاعدة البيانات إذا لم يكن موجوداً
        try:
            self.db.add_user(username, role='employee')
        except:
            pass  # العامل موجود بالفعل

        # الانتقال إلى الشاشة الرئيسية
        self.show_main_screen()

    def set_language(self, language):
        """تغيير اللغة مع تأثير بصري"""
        self.language_var.set(language)
        self.translator.set_language(language)

        # تأثير انتقال سلس
        self.root.after(100, self.show_login_screen)
        
    def admin_login(self):
        """تسجيل دخول المدير"""
        username = self.admin_username_entry.get().strip()
        password = self.admin_password_entry.get().strip()

        if not username or not password:
            messagebox.showerror(
                self.translator.get('error'),
                "يرجى إدخال اسم المستخدم وكلمة المرور"
            )
            return

        user_data = self.db.authenticate_user(username, password)
        if user_data and user_data[1] == 'admin':
            self.current_user = user_data[0]
            self.current_role = user_data[1]
            self.show_main_screen()
        else:
            messagebox.showerror(
                self.translator.get('error'),
                "بيانات تسجيل الدخول غير صحيحة"
            )
    

    
    def show_main_screen(self):
        """عرض الشاشة الرئيسية العصرية"""
        # مسح المحتوى الحالي
        for widget in self.root.winfo_children():
            widget.destroy()

        self.root.title("💈 " + self.translator.get('main_title'))

        # خلفية رئيسية عصرية
        main_canvas = tk.Canvas(self.root, bg=self.colors['bg_primary'], highlightthickness=0)
        main_canvas.pack(fill='both', expand=True)

        # شريط علوي عصري
        header_frame = ttk_bs.Frame(main_canvas, bootstyle="dark")
        header_frame.pack(fill='x', padx=0, pady=0)

        # إطار الترحيب مع تدرج
        welcome_frame = ttk_bs.Frame(header_frame, bootstyle="danger")
        welcome_frame.pack(fill='x', pady=0)

        # معلومات المستخدم
        user_info_frame = ttk_bs.Frame(welcome_frame, bootstyle="danger")
        user_info_frame.pack(fill='x', padx=20, pady=15)

        # أيقونة المستخدم والترحيب
        user_welcome_frame = ttk_bs.Frame(user_info_frame, bootstyle="danger")
        user_welcome_frame.pack(side='left')

        # تحديد أيقونة المستخدم
        user_icon = "👨‍💼" if self.current_role == 'admin' else "👷‍♂️"
        role_text = "المدير" if self.current_role == 'admin' else "العامل"

        user_icon_label = ttk_bs.Label(
            user_welcome_frame,
            text=user_icon,
            font=('Segoe UI Emoji', 24),
            bootstyle="inverse-danger"
        )
        user_icon_label.pack(side='left', padx=(0, 10))

        user_info_text_frame = ttk_bs.Frame(user_welcome_frame, bootstyle="danger")
        user_info_text_frame.pack(side='left')

        welcome_label = ttk_bs.Label(
            user_info_text_frame,
            text=f"مرحباً {role_text}",
            font=self.fonts['subtitle_large'],
            bootstyle="inverse-danger"
        )
        welcome_label.pack(anchor='w')

        username_label = ttk_bs.Label(
            user_info_text_frame,
            text=self.current_user,
            font=self.fonts['subtitle_medium'],
            bootstyle="inverse-danger"
        )
        username_label.pack(anchor='w')

        # أزرار التحكم العلوية
        controls_frame = ttk_bs.Frame(user_info_frame, bootstyle="danger")
        controls_frame.pack(side='right')

        # زر تغيير اللغة
        lang_btn = ttk_bs.Button(
            controls_frame,
            text="🌐 " + self.translator.get('language'),
            command=self.show_language_menu,
            bootstyle="outline-light",
            width=12
        )
        lang_btn.pack(side='right', padx=5)

        # زر تسجيل الخروج
        logout_btn = ttk_bs.Button(
            controls_frame,
            text="🚪 " + self.translator.get('logout'),
            command=self.logout,
            bootstyle="outline-light",
            width=12
        )
        logout_btn.pack(side='right', padx=5)
        
        # إطار المحتوى الرئيسي
        main_content_frame = ttk_bs.Frame(main_canvas, style='Card.TFrame')
        main_content_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التنقل العصري
        nav_frame = ttk_bs.Frame(main_content_frame, bootstyle="secondary")
        nav_frame.pack(fill='x', pady=(0, 10))

        # عنوان التنقل
        nav_title_frame = ttk_bs.Frame(nav_frame, bootstyle="secondary")
        nav_title_frame.pack(fill='x', padx=15, pady=10)

        ttk_bs.Label(
            nav_title_frame,
            text="🧭 لوحة التحكم",
            font=self.fonts['subtitle_medium'],
            bootstyle="inverse-secondary"
        ).pack(side='left')

        # أزرار التنقل العصرية
        nav_buttons_frame = ttk_bs.Frame(nav_frame, bootstyle="secondary")
        nav_buttons_frame.pack(fill='x', padx=15, pady=(0, 15))

        # تعريف أزرار التنقل مع الأيقونات
        nav_buttons = [
            ('customers', self.show_customers_screen, 'info', self.barber_icons['users']),
            ('services', self.show_services_screen, 'warning', self.barber_icons['scissors']),
            ('operations', self.show_operations_screen, 'success', self.barber_icons['chair']),
            ('reports', self.show_reports_screen, 'danger', self.barber_icons['report']),
        ]

        # إنشاء أزرار التنقل
        for i, (key, command, style, icon) in enumerate(nav_buttons):
            btn = ttk_bs.Button(
                nav_buttons_frame,
                text=f"{icon} {self.translator.get(key)}",
                command=command,
                bootstyle=style,
                width=18
            )
            btn.pack(side='left', padx=5, pady=5, ipady=8)

        # إطار المحتوى الديناميكي
        self.content_frame = ttk_bs.Frame(main_content_frame, style='Card.TFrame')
        self.content_frame.pack(fill='both', expand=True, pady=5)

        # عرض لوحة المعلومات افتراضياً
        self.show_dashboard()
    
    def show_language_menu(self):
        """عرض قائمة اختيار اللغة"""
        lang_window = ttk_bs.Toplevel(self.root)
        lang_window.title(self.translator.get('language'))
        lang_window.geometry("200x150")
        lang_window.resizable(False, False)
        
        ttk_bs.Label(lang_window, text=self.translator.get('language')).pack(pady=10)
        
        def set_arabic():
            self.translator.set_language('ar')
            lang_window.destroy()
            self.show_main_screen()
        
        def set_french():
            self.translator.set_language('fr')
            lang_window.destroy()
            self.show_main_screen()
        
        ttk_bs.Button(
            lang_window,
            text=self.translator.get('arabic'),
            command=set_arabic,
            bootstyle="info"
        ).pack(pady=5)
        
        ttk_bs.Button(
            lang_window,
            text=self.translator.get('french'),
            command=set_french,
            bootstyle="info"
        ).pack(pady=5)
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.current_role = None
        self.show_login_screen()
    
    def show_dashboard(self):
        """عرض لوحة المعلومات المتطورة حسب دور المستخدم"""
        if self.current_role == 'admin':
            self.show_admin_dashboard()
        else:
            self.show_employee_dashboard()

    def show_admin_dashboard(self):
        """عرض لوحة تحكم المدير"""
        self.clear_content_frame()

        # إنشاء إطار قابل للتمرير
        main_canvas = tk.Canvas(self.content_frame, bg=self.colors['bg_primary'], highlightthickness=0)
        scrollbar = ttk_bs.Scrollbar(self.content_frame, orient="vertical", command=main_canvas.yview)
        scrollable_frame = ttk_bs.Frame(main_canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # عنوان لوحة المعلومات مع الوقت
        header_frame = ttk_bs.Frame(scrollable_frame)
        header_frame.pack(fill='x', padx=20, pady=(20, 10))

        dashboard_title = ttk_bs.Label(
            header_frame,
            text="📊 لوحة التحكم - المدير",
            font=self.fonts['title_large'],
            bootstyle="primary"
        )
        dashboard_title.pack(side='left')

        # الوقت الحالي
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
        time_label = ttk_bs.Label(
            header_frame,
            text=f"🕐 {current_time}",
            font=self.fonts['body_medium'],
            bootstyle="secondary"
        )
        time_label.pack(side='right')

        # رسالة ترحيب شخصية
        welcome_frame = ttk_bs.LabelFrame(
            scrollable_frame,
            text="👋 مرحباً أيها المدير",
            bootstyle="info",
            padding=15
        )
        welcome_frame.pack(fill='x', padx=20, pady=10)

        welcome_msg = f"أهلاً وسهلاً المدير {self.current_user}! لديك تحكم كامل في إدارة الصالون 💈"

        welcome_label = ttk_bs.Label(
            welcome_frame,
            text=welcome_msg,
            font=self.fonts['body_large'],
            bootstyle="inverse-info",
            wraplength=800
        )
        welcome_label.pack()

        # إحصائيات متقدمة للمدير
        self.create_admin_stats_section(scrollable_frame)

        # الأنشطة الحديثة
        self.create_recent_activities_section(scrollable_frame)

        # إجراءات سريعة للمدير
        self.create_admin_quick_actions(scrollable_frame)

        # معلومات الصالون
        self.create_salon_info_section(scrollable_frame)

        # إضافة تمرير بالماوس
        def on_mousewheel(event):
            main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        main_canvas.bind("<MouseWheel>", on_mousewheel)

    def show_employee_dashboard(self):
        """عرض لوحة تحكم العامل"""
        self.clear_content_frame()

        # إنشاء إطار قابل للتمرير
        main_canvas = tk.Canvas(self.content_frame, bg=self.colors['bg_primary'], highlightthickness=0)
        scrollbar = ttk_bs.Scrollbar(self.content_frame, orient="vertical", command=main_canvas.yview)
        scrollable_frame = ttk_bs.Frame(main_canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # عنوان لوحة العامل
        header_frame = ttk_bs.Frame(scrollable_frame)
        header_frame.pack(fill='x', padx=20, pady=(20, 10))

        dashboard_title = ttk_bs.Label(
            header_frame,
            text="👷‍♂️ لوحة تحكم العامل",
            font=self.fonts['title_large'],
            bootstyle="success"
        )
        dashboard_title.pack(side='left')

        # الوقت الحالي
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
        time_label = ttk_bs.Label(
            header_frame,
            text=f"🕐 {current_time}",
            font=self.fonts['body_medium'],
            bootstyle="secondary"
        )
        time_label.pack(side='right')

        # رسالة ترحيب للعامل
        welcome_frame = ttk_bs.LabelFrame(
            scrollable_frame,
            text="👋 مرحباً بك",
            bootstyle="success",
            padding=15
        )
        welcome_frame.pack(fill='x', padx=20, pady=10)

        welcome_msg = f"أهلاً وسهلاً العامل {self.current_user}! نتمنى لك يوم عمل مثمر وناجح 💈"

        welcome_label = ttk_bs.Label(
            welcome_frame,
            text=welcome_msg,
            font=self.fonts['body_large'],
            bootstyle="inverse-success",
            wraplength=800
        )
        welcome_label.pack()

        # إحصائيات العامل
        self.create_employee_stats_section(scrollable_frame)

        # عمليات العامل الحديثة
        self.create_employee_activities_section(scrollable_frame)

        # إجراءات سريعة للعامل
        self.create_employee_quick_actions(scrollable_frame)

        # نصائح وإرشادات للعامل
        self.create_employee_tips_section(scrollable_frame)

        # إضافة تمرير بالماوس
        def on_mousewheel(event):
            main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        main_canvas.bind("<MouseWheel>", on_mousewheel)

    def create_admin_stats_section(self, parent):
        """إنشاء قسم إحصائيات المدير"""
        stats_section = ttk_bs.LabelFrame(
            parent,
            text="📈 الإحصائيات المتقدمة",
            bootstyle="success",
            padding=20
        )
        stats_section.pack(fill='x', padx=20, pady=10)

        try:
            # الحصول على البيانات
            customers = self.db.get_customers()
            services = self.db.get_services()
            operations = self.db.get_operations()
            users = self.db.get_users()

            # حساب الإحصائيات
            customers_count = len(customers)
            services_count = len(services)
            operations_count = len(operations)
            employees_count = len([u for u in users if u[1] == 'employee'])

            # حساب الأرباح التقديرية
            total_revenue = operations_count * 25  # متوسط 25 للعملية
            today_operations = min(operations_count, 10)  # تقدير عمليات اليوم

            # الصف الأول من البطاقات
            row1_frame = ttk_bs.Frame(stats_section)
            row1_frame.pack(fill='x', pady=(0, 10))

            stats_row1 = [
                ("👥", "إجمالي الزبائن", customers_count, "info", "زبون مسجل"),
                ("✂️", "الخدمات المتاحة", services_count, "warning", "خدمة مختلفة"),
                ("💺", "العمليات المنجزة", operations_count, "success", "عملية مكتملة"),
                ("👷‍♂️", "العاملون", employees_count, "primary", "عامل نشط")
            ]

            for icon, title, value, style, subtitle in stats_row1:
                self.create_stat_card(row1_frame, icon, title, value, style, subtitle)

            # الصف الثاني من البطاقات
            row2_frame = ttk_bs.Frame(stats_section)
            row2_frame.pack(fill='x', pady=(10, 0))

            stats_row2 = [
                ("💰", "الأرباح التقديرية", f"{total_revenue} ج.م", "danger", "إجمالي تقديري"),
                ("📅", "عمليات اليوم", today_operations, "secondary", "عملية اليوم"),
                ("⭐", "متوسط التقييم", "4.8/5", "info", "تقييم الزبائن"),
                ("🏆", "حالة الصالون", "ممتاز", "success", "الأداء العام")
            ]

            for icon, title, value, style, subtitle in stats_row2:
                self.create_stat_card(row2_frame, icon, title, value, style, subtitle)

        except Exception as e:
            error_label = ttk_bs.Label(
                stats_section,
                text=f"❌ خطأ في تحميل الإحصائيات: {str(e)}",
                font=self.fonts['body_medium'],
                bootstyle="danger"
            )
            error_label.pack(pady=20)

    def create_employee_stats_section(self, parent):
        """إنشاء قسم إحصائيات العامل"""
        stats_section = ttk_bs.LabelFrame(
            parent,
            text="📊 إحصائياتك الشخصية",
            bootstyle="warning",
            padding=20
        )
        stats_section.pack(fill='x', padx=20, pady=10)

        try:
            # الحصول على عمليات العامل
            all_operations = self.db.get_operations()
            employee_operations = [op for op in all_operations if len(op) > 5 and op[5] == self.current_user]

            # حساب الإحصائيات
            total_operations = len(employee_operations)
            total_earnings = sum(float(op[3]) for op in employee_operations if op[3])

            # عمليات اليوم
            import datetime
            today = datetime.date.today().strftime("%Y-%m-%d")
            today_operations = [op for op in employee_operations if op[4].startswith(today)]
            today_count = len(today_operations)
            today_earnings = sum(float(op[3]) for op in today_operations if op[3])

            # الصف الأول من الإحصائيات
            row1_frame = ttk_bs.Frame(stats_section)
            row1_frame.pack(fill='x', pady=(0, 10))

            stats_row1 = [
                ("💺", "إجمالي عملياتك", total_operations, "success", "عملية مكتملة"),
                ("💰", "إجمالي أرباحك", f"{total_earnings:.0f} ج.م", "info", "الأرباح الكلية"),
                ("📅", "عمليات اليوم", today_count, "warning", "عمليات اليوم"),
                ("🏆", "أرباح اليوم", f"{today_earnings:.0f} ج.م", "danger", "أرباح اليوم")
            ]

            for icon, title, value, style, subtitle in stats_row1:
                self.create_stat_card(row1_frame, icon, title, value, style, subtitle)

            # الصف الثاني من الإحصائيات
            row2_frame = ttk_bs.Frame(stats_section)
            row2_frame.pack(fill='x')

            # حساب متوسط العمليات والتقييم
            avg_daily = total_operations / 30 if total_operations > 0 else 0  # متوسط شهري
            rating = min(5.0, 3.5 + (total_operations * 0.01))  # تقييم تقديري

            stats_row2 = [
                ("📈", "متوسط يومي", f"{avg_daily:.1f}", "secondary", "عملية/يوم"),
                ("⭐", "تقييمك", f"{rating:.1f}/5", "success", "تقييم الزبائن"),
                ("🎯", "هدف اليوم", "10", "primary", "عملية مطلوبة"),
                ("🏅", "مستواك", "ممتاز" if total_operations > 50 else "جيد", "warning", "الأداء العام")
            ]

            for icon, title, value, style, subtitle in stats_row2:
                self.create_stat_card(row2_frame, icon, title, value, style, subtitle)

        except Exception as e:
            error_label = ttk_bs.Label(
                stats_section,
                text=f"❌ خطأ في تحميل إحصائياتك: {str(e)}",
                font=self.fonts['body_medium'],
                bootstyle="danger"
            )
            error_label.pack(pady=20)

    def create_stat_card(self, parent, icon, title, value, style, subtitle):
        """إنشاء بطاقة إحصائية"""
        card = ttk_bs.LabelFrame(
            parent,
            text="",
            bootstyle=style,
            padding=15
        )
        card.pack(side='left', padx=5, pady=5, fill='both', expand=True)

        # الأيقونة
        icon_label = ttk_bs.Label(
            card,
            text=icon,
            font=('Segoe UI Emoji', 24),
            bootstyle=f"inverse-{style}"
        )
        icon_label.pack(pady=(0, 5))

        # العنوان
        title_label = ttk_bs.Label(
            card,
            text=title,
            font=self.fonts['subtitle_small'],
            bootstyle=f"inverse-{style}"
        )
        title_label.pack()

        # القيمة
        value_label = ttk_bs.Label(
            card,
            text=str(value),
            font=('Segoe UI', 20, 'bold'),
            bootstyle=f"inverse-{style}"
        )
        value_label.pack(pady=5)

        # العنوان الفرعي
        subtitle_label = ttk_bs.Label(
            card,
            text=subtitle,
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{style}"
        )
        subtitle_label.pack()

    def create_recent_activities_section(self, parent):
        """إنشاء قسم الأنشطة الحديثة"""
        activities_section = ttk_bs.LabelFrame(
            parent,
            text="🕐 الأنشطة الحديثة",
            bootstyle="warning",
            padding=20
        )
        activities_section.pack(fill='x', padx=20, pady=10)

        # إطار الأنشطة
        activities_frame = ttk_bs.Frame(activities_section)
        activities_frame.pack(fill='x')

        try:
            # الحصول على آخر العمليات
            operations = self.db.get_operations()
            recent_operations = operations[-5:] if operations else []

            if recent_operations:
                for i, operation in enumerate(reversed(recent_operations)):
                    self.create_activity_item(activities_frame, operation, i)
            else:
                # رسالة عدم وجود أنشطة
                no_activities = ttk_bs.Label(
                    activities_frame,
                    text="📝 لا توجد أنشطة حديثة",
                    font=self.fonts['body_medium'],
                    bootstyle="secondary"
                )
                no_activities.pack(pady=20)

        except Exception as e:
            error_label = ttk_bs.Label(
                activities_frame,
                text=f"❌ خطأ في تحميل الأنشطة: {str(e)}",
                font=self.fonts['body_medium'],
                bootstyle="danger"
            )
            error_label.pack(pady=20)

    def create_activity_item(self, parent, operation, index):
        """إنشاء عنصر نشاط"""
        # إطار النشاط
        activity_frame = ttk_bs.Frame(parent)
        activity_frame.pack(fill='x', pady=2)

        # لون متناوب للصفوف
        bg_style = "light" if index % 2 == 0 else "secondary"

        activity_card = ttk_bs.Frame(activity_frame, bootstyle=bg_style, padding=10)
        activity_card.pack(fill='x')

        # الأيقونة والوقت
        left_frame = ttk_bs.Frame(activity_card, bootstyle=bg_style)
        left_frame.pack(side='left', fill='y')

        # أيقونة النشاط
        activity_icon = ttk_bs.Label(
            left_frame,
            text="✂️",
            font=('Segoe UI Emoji', 16),
            bootstyle=f"inverse-{bg_style}"
        )
        activity_icon.pack(side='left', padx=(0, 10))

        # معلومات النشاط
        info_frame = ttk_bs.Frame(activity_card, bootstyle=bg_style)
        info_frame.pack(side='left', fill='both', expand=True)

        # النشاط
        activity_text = f"عملية جديدة للزبون: {operation[1]}"
        activity_label = ttk_bs.Label(
            info_frame,
            text=activity_text,
            font=self.fonts['body_medium'],
            bootstyle=f"inverse-{bg_style}"
        )
        activity_label.pack(anchor='w')

        # التفاصيل
        details_text = f"الخدمة: {operation[2]} | السعر: {operation[3]} ج.م"
        details_label = ttk_bs.Label(
            info_frame,
            text=details_text,
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{bg_style}"
        )
        details_label.pack(anchor='w')

        # الوقت
        time_frame = ttk_bs.Frame(activity_card, bootstyle=bg_style)
        time_frame.pack(side='right')

        time_text = operation[4][:16] if len(operation) > 4 else "غير محدد"
        time_label = ttk_bs.Label(
            time_frame,
            text=time_text,
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{bg_style}"
        )
        time_label.pack()

    def create_employee_activities_section(self, parent):
        """إنشاء قسم أنشطة العامل"""
        activities_section = ttk_bs.LabelFrame(
            parent,
            text="🕐 عملياتك الحديثة",
            bootstyle="info",
            padding=20
        )
        activities_section.pack(fill='x', padx=20, pady=10)

        # إطار الأنشطة
        activities_frame = ttk_bs.Frame(activities_section)
        activities_frame.pack(fill='x')

        try:
            # الحصول على عمليات العامل
            all_operations = self.db.get_operations()
            employee_operations = [op for op in all_operations if len(op) > 5 and op[5] == self.current_user]
            recent_operations = employee_operations[-5:] if employee_operations else []

            if recent_operations:
                for i, operation in enumerate(reversed(recent_operations)):
                    self.create_employee_activity_item(activities_frame, operation, i)
            else:
                # رسالة عدم وجود عمليات
                no_activities = ttk_bs.Label(
                    activities_frame,
                    text="📝 لم تقم بأي عمليات بعد. ابدأ عملك الآن!",
                    font=self.fonts['body_medium'],
                    bootstyle="secondary"
                )
                no_activities.pack(pady=20)

        except Exception as e:
            error_label = ttk_bs.Label(
                activities_frame,
                text=f"❌ خطأ في تحميل عملياتك: {str(e)}",
                font=self.fonts['body_medium'],
                bootstyle="danger"
            )
            error_label.pack(pady=20)

    def create_employee_activity_item(self, parent, operation, index):
        """إنشاء عنصر نشاط للعامل"""
        # إطار النشاط
        activity_frame = ttk_bs.Frame(parent)
        activity_frame.pack(fill='x', pady=2)

        # لون متناوب للصفوف
        bg_style = "success" if index % 2 == 0 else "info"

        activity_card = ttk_bs.Frame(activity_frame, bootstyle=bg_style, padding=10)
        activity_card.pack(fill='x')

        # الأيقونة والمعلومات
        left_frame = ttk_bs.Frame(activity_card, bootstyle=bg_style)
        left_frame.pack(side='left', fill='y')

        # أيقونة العملية
        activity_icon = ttk_bs.Label(
            left_frame,
            text="✂️",
            font=('Segoe UI Emoji', 16),
            bootstyle=f"inverse-{bg_style}"
        )
        activity_icon.pack(side='left', padx=(0, 10))

        # معلومات العملية
        info_frame = ttk_bs.Frame(activity_card, bootstyle=bg_style)
        info_frame.pack(side='left', fill='both', expand=True)

        # العملية
        activity_text = f"قمت بخدمة الزبون: {operation[1]}"
        activity_label = ttk_bs.Label(
            info_frame,
            text=activity_text,
            font=self.fonts['body_medium'],
            bootstyle=f"inverse-{bg_style}"
        )
        activity_label.pack(anchor='w')

        # التفاصيل
        details_text = f"الخدمة: {operation[2]} | ربحت: {operation[3]} ج.م"
        details_label = ttk_bs.Label(
            info_frame,
            text=details_text,
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{bg_style}"
        )
        details_label.pack(anchor='w')

        # الوقت والتقييم
        right_frame = ttk_bs.Frame(activity_card, bootstyle=bg_style)
        right_frame.pack(side='right')

        time_text = operation[4][:16] if len(operation) > 4 else "غير محدد"
        time_label = ttk_bs.Label(
            right_frame,
            text=time_text,
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{bg_style}"
        )
        time_label.pack()

        # تقييم وهمي
        rating_label = ttk_bs.Label(
            right_frame,
            text="⭐⭐⭐⭐⭐",
            font=self.fonts['body_small'],
            bootstyle=f"inverse-{bg_style}"
        )
        rating_label.pack()

    def create_admin_quick_actions(self, parent):
        """إنشاء قسم الإجراءات السريعة للمدير"""
        actions_section = ttk_bs.LabelFrame(
            parent,
            text="⚡ الإجراءات السريعة",
            bootstyle="primary",
            padding=20
        )
        actions_section.pack(fill='x', padx=20, pady=10)

        # الصف الأول من الإجراءات
        row1_frame = ttk_bs.Frame(actions_section)
        row1_frame.pack(fill='x', pady=(0, 10))

        primary_actions = [
            ("👤 إضافة زبون جديد", "إضافة زبون إلى النظام", self.show_add_customer_dialog, "info"),
            ("💺 عملية جديدة", "تسجيل عملية حلاقة", self.show_add_operation_dialog, "success"),
            ("✂️ إدارة الخدمات", "تعديل قائمة الخدمات", self.show_services_screen, "warning")
        ]

        for title, desc, command, style in primary_actions:
            self.create_action_button(row1_frame, title, desc, command, style)

        # الصف الثاني من الإجراءات
        row2_frame = ttk_bs.Frame(actions_section)
        row2_frame.pack(fill='x')

        secondary_actions = [
            ("📊 التقارير", "عرض تقارير مفصلة", self.show_reports_screen, "danger"),
            ("👥 إدارة الزبائن", "عرض وتعديل الزبائن", self.show_customers_screen, "secondary"),
            ("⚙️ الإعدادات", "إعدادات النظام", self.show_settings_screen, "dark")
        ]

        for title, desc, command, style in secondary_actions:
            self.create_action_button(row2_frame, title, desc, command, style)

    def create_action_button(self, parent, title, description, command, style):
        """إنشاء زر إجراء محسن"""
        # إطار الزر
        button_frame = ttk_bs.Frame(parent)
        button_frame.pack(side='left', padx=5, pady=5, fill='both', expand=True)

        # الزر الرئيسي
        action_btn = ttk_bs.Button(
            button_frame,
            text=title,
            command=command,
            bootstyle=style,
            width=20
        )
        action_btn.pack(fill='x', ipady=15)

        # الوصف
        desc_label = ttk_bs.Label(
            button_frame,
            text=description,
            font=self.fonts['body_small'],
            bootstyle="secondary",
            wraplength=150
        )
        desc_label.pack(pady=(5, 0))

        # تأثير التمرير
        def on_enter(event):
            action_btn.configure(bootstyle=f"outline-{style}")

        def on_leave(event):
            action_btn.configure(bootstyle=style)

        action_btn.bind("<Enter>", on_enter)
        action_btn.bind("<Leave>", on_leave)

    def create_employee_quick_actions(self, parent):
        """إنشاء قسم الإجراءات السريعة للعامل"""
        actions_section = ttk_bs.LabelFrame(
            parent,
            text="⚡ إجراءاتك السريعة",
            bootstyle="warning",
            padding=20
        )
        actions_section.pack(fill='x', padx=20, pady=10)

        # الصف الأول من الإجراءات
        row1_frame = ttk_bs.Frame(actions_section)
        row1_frame.pack(fill='x', pady=(0, 10))

        employee_actions = [
            ("💺 عملية جديدة", "تسجيل عملية حلاقة جديدة", self.show_add_operation_dialog, "success"),
            ("👤 إضافة زبون", "إضافة زبون جديد للنظام", self.show_add_customer_dialog, "info"),
            ("📊 عملياتي", "عرض عملياتي وأرباحي", self.show_my_operations, "primary")
        ]

        for title, desc, command, style in employee_actions:
            self.create_action_button(row1_frame, title, desc, command, style)

        # الصف الثاني من الإجراءات
        row2_frame = ttk_bs.Frame(actions_section)
        row2_frame.pack(fill='x')

        more_actions = [
            ("👥 قائمة الزبائن", "عرض جميع الزبائن", self.show_customers_screen, "secondary"),
            ("✂️ قائمة الخدمات", "عرض الخدمات المتاحة", self.show_services_screen, "warning"),
            ("🏆 إنجازاتي", "عرض إنجازاتي وتقييماتي", self.show_my_achievements, "danger")
        ]

        for title, desc, command, style in more_actions:
            self.create_action_button(row2_frame, title, desc, command, style)

    def show_my_operations(self):
        """عرض عمليات العامل"""
        self.clear_content_frame()

        # عنوان الصفحة
        title = ttk_bs.Label(
            self.content_frame,
            text=f"💺 عملياتي - {self.current_user}",
            font=self.fonts['title_large'],
            bootstyle="primary"
        )
        title.pack(pady=20)

        # إطار الجدول
        table_frame = ttk_bs.Frame(self.content_frame)
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # إنشاء الجدول
        columns = ("التاريخ", "الزبون", "الخدمة", "السعر", "الملاحظات")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor='center')

        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # تحميل البيانات
        try:
            all_operations = self.db.get_operations()
            employee_operations = [op for op in all_operations if len(op) > 5 and op[5] == self.current_user]

            for operation in employee_operations:
                tree.insert("", "end", values=(
                    operation[4][:10],  # التاريخ
                    operation[1],       # الزبون
                    operation[2],       # الخدمة
                    f"{operation[3]} ج.م",  # السعر
                    operation[6] if len(operation) > 6 else "لا توجد"  # الملاحظات
                ))

            # إحصائية سريعة
            total_operations = len(employee_operations)
            total_earnings = sum(float(op[3]) for op in employee_operations if op[3])

            stats_label = ttk_bs.Label(
                self.content_frame,
                text=f"📊 إجمالي العمليات: {total_operations} | إجمالي الأرباح: {total_earnings:.0f} ج.م",
                font=self.fonts['body_large'],
                bootstyle="success"
            )
            stats_label.pack(pady=10)

        except Exception as e:
            error_label = ttk_bs.Label(
                table_frame,
                text=f"❌ خطأ في تحميل العمليات: {str(e)}",
                font=self.fonts['body_medium'],
                bootstyle="danger"
            )
            error_label.pack(pady=20)

    def show_my_achievements(self):
        """عرض إنجازات العامل"""
        self.clear_content_frame()

        # عنوان الصفحة
        title = ttk_bs.Label(
            self.content_frame,
            text=f"🏆 إنجازاتي - {self.current_user}",
            font=self.fonts['title_large'],
            bootstyle="warning"
        )
        title.pack(pady=20)

        # إطار الإنجازات
        achievements_frame = ttk_bs.Frame(self.content_frame)
        achievements_frame.pack(fill='both', expand=True, padx=20, pady=10)

        try:
            # حساب الإنجازات
            all_operations = self.db.get_operations()
            employee_operations = [op for op in all_operations if len(op) > 5 and op[5] == self.current_user]
            total_operations = len(employee_operations)
            total_earnings = sum(float(op[3]) for op in employee_operations if op[3])

            # قائمة الإنجازات
            achievements = []

            if total_operations >= 1:
                achievements.append(("🎯", "البداية", "أول عملية", "success"))
            if total_operations >= 10:
                achievements.append(("⭐", "نجم صاعد", "10 عمليات", "info"))
            if total_operations >= 25:
                achievements.append(("🏅", "محترف", "25 عملية", "warning"))
            if total_operations >= 50:
                achievements.append(("👑", "خبير", "50 عملية", "danger"))
            if total_earnings >= 500:
                achievements.append(("💰", "مربح", "500 ج.م أرباح", "success"))
            if total_earnings >= 1000:
                achievements.append(("💎", "ماسي", "1000 ج.م أرباح", "primary"))

            # عرض الإنجازات
            if achievements:
                for icon, title, desc, style in achievements:
                    achievement_card = ttk_bs.LabelFrame(
                        achievements_frame,
                        text=f"{icon} {title}",
                        bootstyle=style,
                        padding=15
                    )
                    achievement_card.pack(fill='x', pady=5)

                    desc_label = ttk_bs.Label(
                        achievement_card,
                        text=desc,
                        font=self.fonts['body_medium'],
                        bootstyle=f"inverse-{style}"
                    )
                    desc_label.pack()
            else:
                no_achievements = ttk_bs.Label(
                    achievements_frame,
                    text="🎯 ابدأ العمل لتحصل على إنجازات!",
                    font=self.fonts['body_large'],
                    bootstyle="secondary"
                )
                no_achievements.pack(pady=50)

        except Exception as e:
            error_label = ttk_bs.Label(
                achievements_frame,
                text=f"❌ خطأ في تحميل الإنجازات: {str(e)}",
                font=self.fonts['body_medium'],
                bootstyle="danger"
            )
            error_label.pack(pady=20)

    def create_employee_tips_section(self, parent):
        """إنشاء قسم النصائح والإرشادات للعامل"""
        tips_section = ttk_bs.LabelFrame(
            parent,
            text="💡 نصائح وإرشادات",
            bootstyle="secondary",
            padding=20
        )
        tips_section.pack(fill='x', padx=20, pady=10)

        # نصائح للعامل
        tips = [
            "🎯 اهدف لإنجاز 10 عمليات يومياً لتحقيق أفضل النتائج",
            "😊 ابتسم دائماً واستقبل الزبائن بود وترحاب",
            "✂️ حافظ على نظافة أدواتك وتعقيمها باستمرار",
            "⏰ احترم مواعيد الزبائن ولا تتأخر عليهم",
            "🎨 تعلم تقنيات جديدة لتطوير مهاراتك",
            "💬 استمع لطلبات الزبائن بعناية قبل البدء"
        ]

        # عرض النصائح
        for i, tip in enumerate(tips):
            tip_frame = ttk_bs.Frame(tips_section)
            tip_frame.pack(fill='x', pady=2)

            tip_label = ttk_bs.Label(
                tip_frame,
                text=tip,
                font=self.fonts['body_medium'],
                bootstyle="inverse-secondary",
                wraplength=700
            )
            tip_label.pack(anchor='w', padx=10)

        # رسالة تحفيزية
        motivation_frame = ttk_bs.Frame(tips_section)
        motivation_frame.pack(fill='x', pady=(15, 0))

        motivation_label = ttk_bs.Label(
            motivation_frame,
            text="🌟 أنت جزء مهم من فريق الصالون. عملك الجيد يساهم في نجاحنا جميعاً!",
            font=self.fonts['body_large'],
            bootstyle="inverse-secondary",
            wraplength=700
        )
        motivation_label.pack(anchor='center')

    def create_salon_info_section(self, parent):
        """إنشاء قسم معلومات الصالون"""
        info_section = ttk_bs.LabelFrame(
            parent,
            text="💈 معلومات الصالون",
            bootstyle="secondary",
            padding=20
        )
        info_section.pack(fill='x', padx=20, pady=10)

        # معلومات الصالون في عمودين
        info_frame = ttk_bs.Frame(info_section)
        info_frame.pack(fill='x')

        # العمود الأيسر
        left_column = ttk_bs.Frame(info_frame)
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 10))

        salon_info_left = [
            ("🏪", "اسم الصالون", "صالون الحلاقة العصري"),
            ("📍", "العنوان", "شارع الجمهورية، المدينة"),
            ("📞", "الهاتف", "+20 123 456 789"),
        ]

        for icon, label, value in salon_info_left:
            info_row = ttk_bs.Frame(left_column)
            info_row.pack(fill='x', pady=2)

            ttk_bs.Label(
                info_row,
                text=f"{icon} {label}:",
                font=self.fonts['body_medium'],
                bootstyle="inverse-secondary"
            ).pack(side='left')

            ttk_bs.Label(
                info_row,
                text=value,
                font=self.fonts['body_medium'],
                bootstyle="inverse-secondary"
            ).pack(side='right')

        # العمود الأيمن
        right_column = ttk_bs.Frame(info_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))

        salon_info_right = [
            ("🕐", "ساعات العمل", "9:00 ص - 10:00 م"),
            ("👨‍💼", "المدير الحالي", self.current_user),
            ("📅", "تاريخ التأسيس", "2024"),
        ]

        for icon, label, value in salon_info_right:
            info_row = ttk_bs.Frame(right_column)
            info_row.pack(fill='x', pady=2)

            ttk_bs.Label(
                info_row,
                text=f"{icon} {label}:",
                font=self.fonts['body_medium'],
                bootstyle="inverse-secondary"
            ).pack(side='left')

            ttk_bs.Label(
                info_row,
                text=str(value),
                font=self.fonts['body_medium'],
                bootstyle="inverse-secondary"
            ).pack(side='right')

    def show_settings_screen(self):
        """عرض شاشة الإعدادات"""
        self.clear_content_frame()

        # عنوان الإعدادات
        settings_title = ttk_bs.Label(
            self.content_frame,
            text="⚙️ إعدادات النظام",
            font=self.fonts['title_large'],
            bootstyle="primary"
        )
        settings_title.pack(pady=20)

        # إعدادات عامة
        general_frame = ttk_bs.LabelFrame(
            self.content_frame,
            text="🔧 الإعدادات العامة",
            bootstyle="info",
            padding=20
        )
        general_frame.pack(fill='x', padx=20, pady=10)

        # إعدادات اللغة
        lang_frame = ttk_bs.Frame(general_frame)
        lang_frame.pack(fill='x', pady=5)

        ttk_bs.Label(
            lang_frame,
            text="🌐 اللغة:",
            font=self.fonts['body_medium']
        ).pack(side='left')

        lang_var = tk.StringVar(value=self.translator.current_language)
        lang_combo = ttk_bs.Combobox(
            lang_frame,
            textvariable=lang_var,
            values=['ar', 'fr'],
            state='readonly',
            width=10
        )
        lang_combo.pack(side='right')

        # إعدادات النسخ الاحتياطي
        backup_frame = ttk_bs.LabelFrame(
            self.content_frame,
            text="💾 النسخ الاحتياطي",
            bootstyle="warning",
            padding=20
        )
        backup_frame.pack(fill='x', padx=20, pady=10)

        backup_btn = ttk_bs.Button(
            backup_frame,
            text="📤 إنشاء نسخة احتياطية",
            command=self.create_backup,
            bootstyle="warning",
            width=25
        )
        backup_btn.pack(pady=5)

        restore_btn = ttk_bs.Button(
            backup_frame,
            text="📥 استعادة نسخة احتياطية",
            command=self.restore_backup,
            bootstyle="secondary",
            width=25
        )
        restore_btn.pack(pady=5)

        # معلومات النظام
        system_frame = ttk_bs.LabelFrame(
            self.content_frame,
            text="💻 معلومات النظام",
            bootstyle="success",
            padding=20
        )
        system_frame.pack(fill='x', padx=20, pady=10)

        system_info = [
            ("📊 إصدار البرنامج", "1.0.0"),
            ("🗄️ حجم قاعدة البيانات", "2.5 MB"),
            ("👥 عدد المستخدمين", str(len(self.db.get_users()))),
            ("📅 آخر تحديث", "2024-01-01")
        ]

        for label, value in system_info:
            info_row = ttk_bs.Frame(system_frame)
            info_row.pack(fill='x', pady=2)

            ttk_bs.Label(
                info_row,
                text=f"{label}:",
                font=self.fonts['body_medium']
            ).pack(side='left')

            ttk_bs.Label(
                info_row,
                text=value,
                font=self.fonts['body_medium'],
                bootstyle="success"
            ).pack(side='right')

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            import shutil
            import datetime

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.db"

            shutil.copy2("coiffeur.db", backup_name)

            messagebox.showinfo(
                "نجح",
                f"تم إنشاء النسخة الاحتياطية: {backup_name}"
            )
        except Exception as e:
            messagebox.showerror(
                "خطأ",
                f"فشل في إنشاء النسخة الاحتياطية: {str(e)}"
            )

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        from tkinter import filedialog

        try:
            backup_file = filedialog.askopenfilename(
                title="اختر ملف النسخة الاحتياطية",
                filetypes=[("Database files", "*.db")]
            )

            if backup_file:
                import shutil
                shutil.copy2(backup_file, "coiffeur.db")

                messagebox.showinfo(
                    "نجح",
                    "تم استعادة النسخة الاحتياطية بنجاح"
                )

                # إعادة تحميل قاعدة البيانات
                self.db = Database("coiffeur.db")

        except Exception as e:
            messagebox.showerror(
                "خطأ",
                f"فشل في استعادة النسخة الاحتياطية: {str(e)}"
            )

    def clear_content_frame(self):
        """مسح محتوى الإطار الرئيسي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_customers_screen(self):
        """عرض شاشة إدارة الزبائن العصرية"""
        self.clear_content_frame()

        # شريط العنوان والأدوات
        header_frame = ttk_bs.Frame(self.content_frame, bootstyle="info")
        header_frame.pack(fill='x', pady=(0, 15))

        # العنوان مع الأيقونة
        title_frame = ttk_bs.Frame(header_frame, bootstyle="info")
        title_frame.pack(fill='x', padx=20, pady=15)

        title_label = ttk_bs.Label(
            title_frame,
            text=f"{self.barber_icons['users']} {self.translator.get('customers')}",
            font=self.fonts['title_large'],
            bootstyle="inverse-info"
        )
        title_label.pack(side='left')

        # شريط الأدوات
        toolbar_frame = ttk_bs.Frame(header_frame, bootstyle="info")
        toolbar_frame.pack(fill='x', padx=20, pady=(0, 15))

        # أزرار الأدوات العصرية
        add_btn = ttk_bs.Button(
            toolbar_frame,
            text=f"➕ {self.translator.get('add_customer')}",
            command=self.show_add_customer_dialog,
            bootstyle="success",
            width=15
        )
        add_btn.pack(side='left', padx=5, ipady=5)

        refresh_btn = ttk_bs.Button(
            toolbar_frame,
            text=f"🔄 {self.translator.get('refresh')}",
            command=self.refresh_customers_list,
            bootstyle="outline-light",
            width=12
        )
        refresh_btn.pack(side='left', padx=5, ipady=5)

        # شريط البحث
        search_frame = ttk_bs.Frame(toolbar_frame)
        search_frame.pack(side='right', padx=5)

        ttk_bs.Label(search_frame, text="🔍", bootstyle="inverse-info").pack(side='left', padx=5)
        search_entry = ttk_bs.Entry(search_frame, placeholder_text="البحث في الزبائن...", width=20)
        search_entry.pack(side='left', padx=5)

        # إطار الجدول العصري
        table_frame = ttk_bs.LabelFrame(
            self.content_frame,
            text="📋 قائمة الزبائن",
            bootstyle="secondary",
            padding=15
        )
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # إطار الجدول مع التمرير
        tree_frame = ttk_bs.Frame(table_frame)
        tree_frame.pack(fill='both', expand=True)

        # جدول الزبائن العصري
        columns = ('ID', 'name', 'phone', 'added_by', 'date_added')
        self.customers_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show='headings',
            height=15,
            style='Custom.Treeview'
        )

        # تعيين عناوين الأعمدة مع الأيقونات
        headers = {
            'ID': '🆔 ID',
            'name': f'{self.barber_icons["user"]} {self.translator.get("customer_name")}',
            'phone': f'📱 {self.translator.get("phone_number")}',
            'added_by': f'👤 {self.translator.get("added_by")}',
            'date_added': f'📅 {self.translator.get("date_added")}'
        }

        for col, header in headers.items():
            self.customers_tree.heading(col, text=header)

        # تعيين عرض الأعمدة
        self.customers_tree.column('ID', width=80, anchor='center')
        self.customers_tree.column('name', width=250, anchor='w')
        self.customers_tree.column('phone', width=150, anchor='center')
        self.customers_tree.column('added_by', width=150, anchor='center')
        self.customers_tree.column('date_added', width=150, anchor='center')

        # شريط التمرير العصري
        scrollbar_y = ttk_bs.Scrollbar(tree_frame, orient='vertical', command=self.customers_tree.yview)
        scrollbar_x = ttk_bs.Scrollbar(tree_frame, orient='horizontal', command=self.customers_tree.xview)

        self.customers_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # تخطيط الجدول
        self.customers_tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')

        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # تأثيرات بصرية للجدول
        self.customers_tree.tag_configure('evenrow', background='#f8f9fa')
        self.customers_tree.tag_configure('oddrow', background='#ffffff')

        # تحميل البيانات
        self.refresh_customers_list()

    def refresh_customers_list(self):
        """تحديث قائمة الزبائن مع تأثيرات بصرية"""
        # مسح البيانات الحالية
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        try:
            # تحميل البيانات الجديدة
            customers = self.db.get_customers()

            for i, customer in enumerate(customers):
                # تنسيق التاريخ
                date_str = customer[5][:10] if len(customer) > 5 else "غير محدد"

                # تنسيق البيانات
                formatted_data = (
                    customer[0],  # ID
                    customer[1],  # الاسم
                    customer[2] if customer[2] else "غير محدد",  # الهاتف
                    customer[4],  # أضافه
                    date_str     # التاريخ
                )

                # تطبيق تأثير الصفوف المتناوبة
                tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                self.customers_tree.insert('', 'end', values=formatted_data, tags=(tag,))

        except Exception as e:
            # في حالة الخطأ، عرض رسالة
            error_item = self.customers_tree.insert('', 'end', values=(
                "خطأ", f"خطأ في تحميل البيانات: {str(e)}", "", "", ""
            ))
            self.customers_tree.set(error_item, 'name', f"خطأ في تحميل البيانات: {str(e)}")

    def show_add_customer_dialog(self):
        """عرض نافذة إضافة زبون"""
        dialog = ttk_bs.Toplevel(self.root)
        dialog.title(self.translator.get('add_customer'))
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.grab_set()

        # حقل الاسم
        ttk_bs.Label(dialog, text=self.translator.get('customer_name')).pack(pady=5)
        name_entry = ttk_bs.Entry(dialog, width=30, font=('Arial', 12))
        name_entry.pack(pady=5)

        # حقل الهاتف
        ttk_bs.Label(dialog, text=self.translator.get('phone_number')).pack(pady=5)
        phone_entry = ttk_bs.Entry(dialog, width=30, font=('Arial', 12))
        phone_entry.pack(pady=5)

        # حقل الصورة
        ttk_bs.Label(dialog, text=self.translator.get('customer_image')).pack(pady=5)

        image_frame = ttk_bs.Frame(dialog)
        image_frame.pack(pady=5)

        self.selected_image_path = tk.StringVar()
        image_label = ttk_bs.Label(image_frame, textvariable=self.selected_image_path, width=30)
        image_label.pack(side='left', padx=5)

        browse_btn = ttk_bs.Button(
            image_frame,
            text=self.translator.get('browse_image'),
            command=lambda: self.browse_image(self.selected_image_path),
            bootstyle="outline-info"
        )
        browse_btn.pack(side='left', padx=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk_bs.Frame(dialog)
        buttons_frame.pack(pady=20)

        save_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('save'),
            command=lambda: self.save_customer(dialog, name_entry, phone_entry),
            bootstyle="success"
        )
        save_btn.pack(side='left', padx=10)

        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('cancel'),
            command=dialog.destroy,
            bootstyle="secondary"
        )
        cancel_btn.pack(side='left', padx=10)

        name_entry.focus()

    def browse_image(self, image_var):
        """تصفح واختيار صورة"""
        file_path = filedialog.askopenfilename(
            title=self.translator.get('select_image'),
            filetypes=[
                (self.translator.get('image_files'), '*.png *.jpg *.jpeg *.gif *.bmp'),
                ('All files', '*.*')
            ]
        )
        if file_path:
            image_var.set(file_path)

    def save_customer(self, dialog, name_entry, phone_entry):
        """حفظ بيانات الزبون"""
        name = name_entry.get().strip()
        phone = phone_entry.get().strip()
        image_path = self.selected_image_path.get()

        if not name:
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('fill_all_fields')
            )
            return

        try:
            self.db.add_customer(name, phone, image_path, self.current_user)
            messagebox.showinfo(
                self.translator.get('success'),
                self.translator.get('customer_added')
            )
            dialog.destroy()
            self.refresh_customers_list()
        except Exception as e:
            messagebox.showerror(
                self.translator.get('error'),
                str(e)
            )

    def show_services_screen(self):
        """عرض شاشة إدارة الخدمات"""
        self.clear_content_frame()

        # العنوان
        title_label = ttk_bs.Label(
            self.content_frame,
            text=self.translator.get('services'),
            style='Header.TLabel'
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(self.content_frame)
        buttons_frame.pack(fill='x', pady=5)

        if self.current_role == 'admin':  # فقط المدير يمكنه إضافة خدمات
            add_btn = ttk_bs.Button(
                buttons_frame,
                text=self.translator.get('add_service'),
                command=self.show_add_service_dialog,
                bootstyle="success"
            )
            add_btn.pack(side='left', padx=5)

        refresh_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('refresh'),
            command=self.refresh_services_list,
            bootstyle="info"
        )
        refresh_btn.pack(side='left', padx=5)

        # جدول الخدمات
        columns = ('ID', 'name', 'price')
        self.services_tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        self.services_tree.heading('ID', text='ID')
        self.services_tree.heading('name', text=self.translator.get('service_name'))
        self.services_tree.heading('price', text=self.translator.get('price'))

        # تعيين عرض الأعمدة
        self.services_tree.column('ID', width=50)
        self.services_tree.column('name', width=300)
        self.services_tree.column('price', width=150)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(self.content_frame, orient='vertical', command=self.services_tree.yview)
        self.services_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.services_tree.pack(side='left', fill='both', expand=True, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # تحميل البيانات
        self.refresh_services_list()

    def refresh_services_list(self):
        """تحديث قائمة الخدمات"""
        # مسح البيانات الحالية
        for item in self.services_tree.get_children():
            self.services_tree.delete(item)

        # تحميل البيانات الجديدة
        services = self.db.get_services()
        for service in services:
            self.services_tree.insert('', 'end', values=service)

    def show_add_service_dialog(self):
        """عرض نافذة إضافة خدمة"""
        dialog = ttk_bs.Toplevel(self.root)
        dialog.title(self.translator.get('add_service'))
        dialog.geometry("350x200")
        dialog.resizable(False, False)
        dialog.grab_set()

        # حقل اسم الخدمة
        ttk_bs.Label(dialog, text=self.translator.get('service_name')).pack(pady=5)
        name_entry = ttk_bs.Entry(dialog, width=30, font=('Arial', 12))
        name_entry.pack(pady=5)

        # حقل السعر
        ttk_bs.Label(dialog, text=self.translator.get('service_price')).pack(pady=5)
        price_entry = ttk_bs.Entry(dialog, width=30, font=('Arial', 12))
        price_entry.pack(pady=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk_bs.Frame(dialog)
        buttons_frame.pack(pady=20)

        save_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('save'),
            command=lambda: self.save_service(dialog, name_entry, price_entry),
            bootstyle="success"
        )
        save_btn.pack(side='left', padx=10)

        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('cancel'),
            command=dialog.destroy,
            bootstyle="secondary"
        )
        cancel_btn.pack(side='left', padx=10)

        name_entry.focus()

    def save_service(self, dialog, name_entry, price_entry):
        """حفظ بيانات الخدمة"""
        name = name_entry.get().strip()
        price_text = price_entry.get().strip()

        if not name or not price_text:
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('fill_all_fields')
            )
            return

        try:
            price = float(price_text)
            self.db.add_service(name, price)
            messagebox.showinfo(
                self.translator.get('success'),
                self.translator.get('service_added')
            )
            dialog.destroy()
            self.refresh_services_list()
        except ValueError:
            messagebox.showerror(
                self.translator.get('error'),
                "يرجى إدخال سعر صحيح"
            )
        except Exception as e:
            messagebox.showerror(
                self.translator.get('error'),
                str(e)
            )

    def show_operations_screen(self):
        """عرض شاشة العمليات"""
        self.clear_content_frame()

        # العنوان
        title_label = ttk_bs.Label(
            self.content_frame,
            text=self.translator.get('operations'),
            style='Header.TLabel'
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(self.content_frame)
        buttons_frame.pack(fill='x', pady=5)

        add_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('new_operation'),
            command=self.show_add_operation_dialog,
            bootstyle="success"
        )
        add_btn.pack(side='left', padx=5)

        refresh_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('refresh'),
            command=self.refresh_operations_list,
            bootstyle="info"
        )
        refresh_btn.pack(side='left', padx=5)

        # جدول العمليات
        columns = ('ID', 'customer', 'service', 'performed_by', 'customer_owner', 'price', 'employee_share', 'owner_share', 'date')
        self.operations_tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=12)

        # تعيين عناوين الأعمدة
        self.operations_tree.heading('ID', text='ID')
        self.operations_tree.heading('customer', text=self.translator.get('select_customer'))
        self.operations_tree.heading('service', text=self.translator.get('select_service'))
        self.operations_tree.heading('performed_by', text=self.translator.get('performed_by'))
        self.operations_tree.heading('customer_owner', text=self.translator.get('customer_owner'))
        self.operations_tree.heading('price', text=self.translator.get('total_price'))
        self.operations_tree.heading('employee_share', text=self.translator.get('employee_share'))
        self.operations_tree.heading('owner_share', text=self.translator.get('owner_share'))
        self.operations_tree.heading('date', text=self.translator.get('date_added'))

        # تعيين عرض الأعمدة
        self.operations_tree.column('ID', width=50)
        self.operations_tree.column('customer', width=120)
        self.operations_tree.column('service', width=120)
        self.operations_tree.column('performed_by', width=100)
        self.operations_tree.column('customer_owner', width=100)
        self.operations_tree.column('price', width=80)
        self.operations_tree.column('employee_share', width=100)
        self.operations_tree.column('owner_share', width=100)
        self.operations_tree.column('date', width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(self.content_frame, orient='vertical', command=self.operations_tree.yview)
        self.operations_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.operations_tree.pack(side='left', fill='both', expand=True, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # تحميل البيانات
        self.refresh_operations_list()

    def refresh_operations_list(self):
        """تحديث قائمة العمليات"""
        # مسح البيانات الحالية
        for item in self.operations_tree.get_children():
            self.operations_tree.delete(item)

        # تحميل البيانات الجديدة
        operations = self.db.get_operations()
        for operation in operations:
            self.operations_tree.insert('', 'end', values=operation)

    def show_add_operation_dialog(self):
        """عرض نافذة إضافة عملية"""
        dialog = ttk_bs.Toplevel(self.root)
        dialog.title(self.translator.get('new_operation'))
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.grab_set()

        # اختيار الزبون
        ttk_bs.Label(dialog, text=self.translator.get('select_customer')).pack(pady=5)
        customer_var = tk.StringVar()
        customer_combo = ttk_bs.Combobox(dialog, textvariable=customer_var, state='readonly', width=40)

        # تحميل قائمة الزبائن
        customers = self.db.get_customers()
        customer_values = [f"{customer[0]} - {customer[1]}" for customer in customers]
        customer_combo['values'] = customer_values
        customer_combo.pack(pady=5)

        # اختيار الخدمة
        ttk_bs.Label(dialog, text=self.translator.get('select_service')).pack(pady=5)
        service_var = tk.StringVar()
        service_combo = ttk_bs.Combobox(dialog, textvariable=service_var, state='readonly', width=40)

        # تحميل قائمة الخدمات
        services = self.db.get_services()
        service_values = [f"{service[0]} - {service[1]} ({service[2]} ج.م)" for service in services]
        service_combo['values'] = service_values
        service_combo.pack(pady=5)

        # اختيار من نفذ الخدمة
        ttk_bs.Label(dialog, text=self.translator.get('performed_by')).pack(pady=5)
        performed_by_var = tk.StringVar()
        performed_by_combo = ttk_bs.Combobox(dialog, textvariable=performed_by_var, state='readonly', width=40)

        # تحميل قائمة المستخدمين
        users = self.db.get_users()
        user_values = [user[0] for user in users]
        performed_by_combo['values'] = user_values
        performed_by_combo.pack(pady=5)

        # اختيار صاحب الزبون
        ttk_bs.Label(dialog, text=self.translator.get('customer_owner')).pack(pady=5)
        customer_owner_var = tk.StringVar()
        customer_owner_combo = ttk_bs.Combobox(dialog, textvariable=customer_owner_var, state='readonly', width=40)
        customer_owner_combo['values'] = user_values
        customer_owner_combo.pack(pady=5)

        # عرض السعر والأرباح
        price_frame = ttk_bs.LabelFrame(dialog, text=self.translator.get('total_price'), padding=10)
        price_frame.pack(pady=10, padx=20, fill='x')

        price_label = ttk_bs.Label(price_frame, text="0.00 ج.م", font=('Arial', 14, 'bold'))
        price_label.pack()

        employee_share_label = ttk_bs.Label(price_frame, text=f"{self.translator.get('employee_share')}: 0.00 ج.م")
        employee_share_label.pack()

        owner_share_label = ttk_bs.Label(price_frame, text=f"{self.translator.get('owner_share')}: 0.00 ج.م")
        owner_share_label.pack()

        def update_price_calculation(*args):
            """تحديث حساب الأرباح"""
            try:
                service_text = service_var.get()
                performed_by = performed_by_var.get()
                customer_owner = customer_owner_var.get()

                if service_text and performed_by and customer_owner:
                    # استخراج السعر من النص
                    price_start = service_text.rfind('(') + 1
                    price_end = service_text.rfind(' ج.م)')
                    price = float(service_text[price_start:price_end])

                    # حساب الأرباح
                    if performed_by == customer_owner:
                        employee_share = price
                        owner_share = 0
                    else:
                        employee_share = price * 0.5
                        owner_share = price * 0.5

                    price_label.config(text=f"{price:.2f} ج.م")
                    employee_share_label.config(text=f"{self.translator.get('employee_share')}: {employee_share:.2f} ج.م")
                    owner_share_label.config(text=f"{self.translator.get('owner_share')}: {owner_share:.2f} ج.م")
            except:
                pass

        # ربط التحديث بتغيير القيم
        service_var.trace('w', update_price_calculation)
        performed_by_var.trace('w', update_price_calculation)
        customer_owner_var.trace('w', update_price_calculation)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk_bs.Frame(dialog)
        buttons_frame.pack(pady=20)

        save_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('record_operation'),
            command=lambda: self.save_operation(dialog, customer_var, service_var, performed_by_var, customer_owner_var),
            bootstyle="success"
        )
        save_btn.pack(side='left', padx=10)

        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('cancel'),
            command=dialog.destroy,
            bootstyle="secondary"
        )
        cancel_btn.pack(side='left', padx=10)

    def save_operation(self, dialog, customer_var, service_var, performed_by_var, customer_owner_var):
        """حفظ بيانات العملية"""
        customer_text = customer_var.get()
        service_text = service_var.get()
        performed_by = performed_by_var.get()
        customer_owner = customer_owner_var.get()

        if not all([customer_text, service_text, performed_by, customer_owner]):
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('fill_all_fields')
            )
            return

        try:
            # استخراج معرف الزبون
            customer_id = int(customer_text.split(' - ')[0])

            # استخراج معرف الخدمة والسعر
            service_id = int(service_text.split(' - ')[0])
            price_start = service_text.rfind('(') + 1
            price_end = service_text.rfind(' ج.م)')
            price = float(service_text[price_start:price_end])

            # حفظ العملية
            self.db.add_operation(customer_id, service_id, performed_by, customer_owner, price)

            messagebox.showinfo(
                self.translator.get('success'),
                self.translator.get('operation_recorded')
            )
            dialog.destroy()
            self.refresh_operations_list()

        except Exception as e:
            messagebox.showerror(
                self.translator.get('error'),
                str(e)
            )

    def show_reports_screen(self):
        """عرض شاشة التقارير"""
        self.clear_content_frame()

        # العنوان
        title_label = ttk_bs.Label(
            self.content_frame,
            text=self.translator.get('reports'),
            style='Header.TLabel'
        )
        title_label.pack(pady=10)

        # إطار التصفية
        filter_frame = ttk_bs.LabelFrame(self.content_frame, text="تصفية التقارير", padding=10)
        filter_frame.pack(fill='x', pady=10, padx=20)

        # تاريخ البداية
        date_frame = ttk_bs.Frame(filter_frame)
        date_frame.pack(fill='x', pady=5)

        ttk_bs.Label(date_frame, text=self.translator.get('from_date')).pack(side='left', padx=5)
        self.start_date_var = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        start_date_entry = ttk_bs.Entry(date_frame, textvariable=self.start_date_var, width=15)
        start_date_entry.pack(side='left', padx=5)

        ttk_bs.Label(date_frame, text=self.translator.get('to_date')).pack(side='left', padx=5)
        self.end_date_var = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        end_date_entry = ttk_bs.Entry(date_frame, textvariable=self.end_date_var, width=15)
        end_date_entry.pack(side='left', padx=5)

        # اختيار المستخدم (للمدير فقط)
        if self.current_role == 'admin':
            ttk_bs.Label(date_frame, text="المستخدم:").pack(side='left', padx=5)
            self.user_filter_var = tk.StringVar()
            user_combo = ttk_bs.Combobox(date_frame, textvariable=self.user_filter_var, width=15)
            users = self.db.get_users()
            user_values = ['الكل'] + [user[0] for user in users]
            user_combo['values'] = user_values
            user_combo.set('الكل')
            user_combo.pack(side='left', padx=5)

        # زر إنشاء التقرير
        generate_btn = ttk_bs.Button(
            filter_frame,
            text=self.translator.get('generate_report'),
            command=self.generate_report,
            bootstyle="info"
        )
        generate_btn.pack(side='left', padx=10)

        # إطار النتائج
        self.results_frame = ttk_bs.Frame(self.content_frame)
        self.results_frame.pack(fill='both', expand=True, pady=10, padx=20)

        # إنشاء تقرير افتراضي
        self.generate_report()

    def generate_report(self):
        """إنشاء التقرير"""
        # مسح النتائج السابقة
        for widget in self.results_frame.winfo_children():
            widget.destroy()

        start_date = self.start_date_var.get()
        end_date = self.end_date_var.get()

        # تحديد المستخدم للتصفية
        user_filter = None
        if self.current_role == 'admin' and hasattr(self, 'user_filter_var'):
            if self.user_filter_var.get() != 'الكل':
                user_filter = self.user_filter_var.get()
        elif self.current_role == 'employee':
            user_filter = self.current_user

        # الحصول على البيانات
        operations = self.db.get_operations(start_date, end_date, user_filter)

        # حساب الإحصائيات
        total_operations = len(operations)
        total_revenue = sum(op[5] for op in operations)  # السعر الإجمالي
        total_employee_share = sum(op[6] for op in operations)  # نصيب العامل
        total_owner_share = sum(op[7] for op in operations)  # نصيب المالك

        # عرض الإحصائيات
        stats_frame = ttk_bs.LabelFrame(self.results_frame, text="الإحصائيات", padding=10)
        stats_frame.pack(fill='x', pady=5)

        stats_text = f"""
        {self.translator.get('total_operations')}: {total_operations}
        {self.translator.get('total_revenue')}: {total_revenue:.2f} ج.م
        {self.translator.get('employee_earnings')}: {total_employee_share:.2f} ج.م
        {self.translator.get('owner_earnings')}: {total_owner_share:.2f} ج.م
        """

        ttk_bs.Label(stats_frame, text=stats_text, font=('Arial', 12)).pack()

        # جدول العمليات
        operations_frame = ttk_bs.LabelFrame(self.results_frame, text="تفاصيل العمليات", padding=10)
        operations_frame.pack(fill='both', expand=True, pady=5)

        columns = ('customer', 'service', 'performed_by', 'price', 'employee_share', 'date')
        operations_tree = ttk.Treeview(operations_frame, columns=columns, show='headings', height=10)

        # تعيين عناوين الأعمدة
        operations_tree.heading('customer', text='الزبون')
        operations_tree.heading('service', text='الخدمة')
        operations_tree.heading('performed_by', text='نفذها')
        operations_tree.heading('price', text='السعر')
        operations_tree.heading('employee_share', text='نصيب العامل')
        operations_tree.heading('date', text='التاريخ')

        # تعيين عرض الأعمدة
        operations_tree.column('customer', width=150)
        operations_tree.column('service', width=150)
        operations_tree.column('performed_by', width=100)
        operations_tree.column('price', width=100)
        operations_tree.column('employee_share', width=100)
        operations_tree.column('date', width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(operations_frame, orient='vertical', command=operations_tree.yview)
        operations_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة البيانات
        for operation in operations:
            operations_tree.insert('', 'end', values=(
                operation[1],  # اسم الزبون
                operation[2],  # اسم الخدمة
                operation[3],  # من نفذها
                f"{operation[5]:.2f}",  # السعر
                f"{operation[6]:.2f}",  # نصيب العامل
                operation[8][:10]  # التاريخ
            ))

        operations_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # أزرار التصدير
        export_frame = ttk_bs.Frame(self.results_frame)
        export_frame.pack(fill='x', pady=10)

        pdf_btn = ttk_bs.Button(
            export_frame,
            text=self.translator.get('export_pdf'),
            command=lambda: self.export_report('pdf', operations, {
                'total_operations': total_operations,
                'total_revenue': total_revenue,
                'employee_earnings': total_employee_share,
                'owner_earnings': total_owner_share
            }),
            bootstyle="danger"
        )
        pdf_btn.pack(side='left', padx=5)

        excel_btn = ttk_bs.Button(
            export_frame,
            text=self.translator.get('export_excel'),
            command=lambda: self.export_report('excel', operations, {
                'total_operations': total_operations,
                'total_revenue': total_revenue,
                'employee_earnings': total_employee_share,
                'owner_earnings': total_owner_share
            }),
            bootstyle="success"
        )
        excel_btn.pack(side='left', padx=5)

    def export_report(self, format_type, operations, stats):
        """تصدير التقرير"""
        try:
            from reports import ReportGenerator

            # اختيار مكان الحفظ
            if format_type == 'pdf':
                filename = filedialog.asksaveasfilename(
                    defaultextension=".pdf",
                    filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                    title="حفظ التقرير كـ PDF"
                )
            else:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                    title="حفظ التقرير كـ Excel"
                )

            if filename:
                report_gen = ReportGenerator(self.translator)

                if format_type == 'pdf':
                    success = report_gen.export_to_pdf(operations, stats, filename)
                else:
                    success = report_gen.export_to_excel(operations, stats, filename)

                if success:
                    messagebox.showinfo(
                        self.translator.get('success'),
                        f"تم تصدير التقرير بنجاح إلى:\n{filename}"
                    )
                else:
                    messagebox.showerror(
                        self.translator.get('error'),
                        "فشل في تصدير التقرير"
                    )
        except ImportError:
            messagebox.showerror(
                self.translator.get('error'),
                "مكتبات التصدير غير متوفرة. يرجى تثبيت reportlab و openpyxl"
            )
        except Exception as e:
            messagebox.showerror(
                self.translator.get('error'),
                f"خطأ في التصدير: {str(e)}"
            )

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CoiffeurApp()
    app.run()
