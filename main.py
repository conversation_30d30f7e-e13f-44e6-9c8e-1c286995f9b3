import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from PIL import Image, ImageTk
import os
from datetime import datetime, date
import webbrowser

from database import Database
from translations import Translator
from config import app_config

class CoiffeurApp:
    def __init__(self):
        self.db = Database(app_config.get_database_path())
        self.translator = Translator(app_config.get_language())
        self.current_user = None
        self.current_role = None

        # إنشاء النافذة الرئيسية
        self.root = ttk_bs.Window(themename=app_config.get_theme())
        self.root.title(self.translator.get('login_title'))
        self.root.geometry(app_config.get_window_size())
        self.root.resizable(True, True)
        
        # تعيين الأيقونة
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
        
        # متغيرات الواجهة
        self.setup_styles()
        self.show_login_screen()
        
    def setup_styles(self):
        """إعداد الأنماط المخصصة"""
        style = ttk_bs.Style()
        
        # نمط للعناوين
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Subtitle.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 14, 'bold'), foreground='#2c3e50')
        
        # نمط للأزرار
        style.configure('Action.TButton', font=('Arial', 10, 'bold'))
        
    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول"""
        # مسح المحتوى الحالي
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.root)
        main_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # إطار تسجيل الدخول
        login_frame = ttk_bs.Frame(main_frame, bootstyle="light")
        login_frame.place(relx=0.5, rely=0.5, anchor='center', width=400, height=500)
        
        # العنوان
        title_label = ttk_bs.Label(
            login_frame, 
            text=self.translator.get('login_title'),
            style='Title.TLabel',
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # اختيار اللغة
        lang_frame = ttk_bs.Frame(login_frame)
        lang_frame.pack(pady=10)
        
        ttk_bs.Label(lang_frame, text=self.translator.get('language')).pack(side='left', padx=5)
        
        self.language_var = tk.StringVar(value='ar')
        lang_combo = ttk_bs.Combobox(
            lang_frame, 
            textvariable=self.language_var,
            values=['ar', 'fr'],
            state='readonly',
            width=10
        )
        lang_combo.pack(side='left', padx=5)
        lang_combo.bind('<<ComboboxSelected>>', self.change_language)
        
        # حقول تسجيل الدخول
        ttk_bs.Label(login_frame, text=self.translator.get('username')).pack(pady=(20, 5))
        self.username_entry = ttk_bs.Entry(login_frame, width=30, font=('Arial', 12))
        self.username_entry.pack(pady=5)
        
        ttk_bs.Label(login_frame, text=self.translator.get('password')).pack(pady=(10, 5))
        self.password_entry = ttk_bs.Entry(login_frame, width=30, font=('Arial', 12), show='*')
        self.password_entry.pack(pady=5)
        
        # أزرار تسجيل الدخول
        buttons_frame = ttk_bs.Frame(login_frame)
        buttons_frame.pack(pady=20)
        
        admin_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('admin_login'),
            command=self.admin_login,
            bootstyle="success",
            width=15
        )
        admin_btn.pack(pady=5)
        
        employee_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('employee_login'),
            command=self.employee_login,
            bootstyle="info",
            width=15
        )
        employee_btn.pack(pady=5)
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda e: self.admin_login())
        
    def change_language(self, event=None):
        """تغيير اللغة"""
        new_lang = self.language_var.get()
        self.translator.set_language(new_lang)
        self.show_login_screen()
        
    def admin_login(self):
        """تسجيل دخول المدير"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('fill_all_fields')
            )
            return
        
        user_data = self.db.authenticate_user(username, password)
        if user_data and user_data[1] == 'admin':
            self.current_user = user_data[0]
            self.current_role = user_data[1]
            self.show_main_screen()
        else:
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('invalid_login')
            )
    
    def employee_login(self):
        """تسجيل دخول العامل"""
        username = self.username_entry.get().strip()
        
        if not username:
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('fill_all_fields')
            )
            return
        
        user_data = self.db.authenticate_user(username)
        if user_data:
            self.current_user = user_data[0]
            self.current_role = user_data[1]
            self.show_main_screen()
        else:
            # إضافة العامل تلقائياً إذا لم يكن موجوداً
            if self.db.add_user(username, role='employee'):
                self.current_user = username
                self.current_role = 'employee'
                self.show_main_screen()
            else:
                messagebox.showerror(
                    self.translator.get('error'),
                    self.translator.get('invalid_login')
                )
    
    def show_main_screen(self):
        """عرض الشاشة الرئيسية"""
        # مسح المحتوى الحالي
        for widget in self.root.winfo_children():
            widget.destroy()
        
        self.root.title(self.translator.get('main_title'))
        
        # إطار علوي للترحيب والتنقل
        top_frame = ttk_bs.Frame(self.root, bootstyle="primary")
        top_frame.pack(fill='x', padx=5, pady=5)
        
        # رسالة ترحيب
        welcome_label = ttk_bs.Label(
            top_frame,
            text=f"{self.translator.get('welcome')} {self.current_user}",
            font=('Arial', 14, 'bold'),
            bootstyle="inverse-primary"
        )
        welcome_label.pack(side='left', padx=10, pady=10)
        
        # زر تغيير اللغة
        lang_btn = ttk_bs.Button(
            top_frame,
            text=self.translator.get('language'),
            command=self.show_language_menu,
            bootstyle="outline-light",
            width=10
        )
        lang_btn.pack(side='right', padx=5, pady=5)
        
        # زر تسجيل الخروج
        logout_btn = ttk_bs.Button(
            top_frame,
            text=self.translator.get('logout'),
            command=self.logout,
            bootstyle="outline-light",
            width=10
        )
        logout_btn.pack(side='right', padx=5, pady=5)
        
        # إطار التنقل الجانبي
        nav_frame = ttk_bs.Frame(self.root, bootstyle="light")
        nav_frame.pack(side='left', fill='y', padx=5, pady=5)
        
        # إطار المحتوى الرئيسي
        self.content_frame = ttk_bs.Frame(self.root)
        self.content_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        # أزرار التنقل
        nav_buttons = [
            ('customers', self.show_customers_screen, 'info'),
            ('services', self.show_services_screen, 'warning'),
            ('operations', self.show_operations_screen, 'success'),
            ('reports', self.show_reports_screen, 'danger'),
        ]
        
        for key, command, style in nav_buttons:
            btn = ttk_bs.Button(
                nav_frame,
                text=self.translator.get(key),
                command=command,
                bootstyle=style,
                width=15
            )
            btn.pack(pady=5, padx=10, fill='x')
        
        # عرض شاشة الزبائن افتراضياً
        self.show_customers_screen()
    
    def show_language_menu(self):
        """عرض قائمة اختيار اللغة"""
        lang_window = ttk_bs.Toplevel(self.root)
        lang_window.title(self.translator.get('language'))
        lang_window.geometry("200x150")
        lang_window.resizable(False, False)
        
        ttk_bs.Label(lang_window, text=self.translator.get('language')).pack(pady=10)
        
        def set_arabic():
            self.translator.set_language('ar')
            lang_window.destroy()
            self.show_main_screen()
        
        def set_french():
            self.translator.set_language('fr')
            lang_window.destroy()
            self.show_main_screen()
        
        ttk_bs.Button(
            lang_window,
            text=self.translator.get('arabic'),
            command=set_arabic,
            bootstyle="info"
        ).pack(pady=5)
        
        ttk_bs.Button(
            lang_window,
            text=self.translator.get('french'),
            command=set_french,
            bootstyle="info"
        ).pack(pady=5)
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.current_role = None
        self.show_login_screen()
    
    def clear_content_frame(self):
        """مسح محتوى الإطار الرئيسي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_customers_screen(self):
        """عرض شاشة إدارة الزبائن"""
        self.clear_content_frame()

        # العنوان
        title_label = ttk_bs.Label(
            self.content_frame,
            text=self.translator.get('customers'),
            style='Header.TLabel'
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(self.content_frame)
        buttons_frame.pack(fill='x', pady=5)

        add_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('add_customer'),
            command=self.show_add_customer_dialog,
            bootstyle="success"
        )
        add_btn.pack(side='left', padx=5)

        refresh_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('refresh'),
            command=self.refresh_customers_list,
            bootstyle="info"
        )
        refresh_btn.pack(side='left', padx=5)

        # جدول الزبائن
        columns = ('ID', 'name', 'phone', 'added_by', 'date_added')
        self.customers_tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        self.customers_tree.heading('ID', text='ID')
        self.customers_tree.heading('name', text=self.translator.get('customer_name'))
        self.customers_tree.heading('phone', text=self.translator.get('phone_number'))
        self.customers_tree.heading('added_by', text=self.translator.get('added_by'))
        self.customers_tree.heading('date_added', text=self.translator.get('date_added'))

        # تعيين عرض الأعمدة
        self.customers_tree.column('ID', width=50)
        self.customers_tree.column('name', width=200)
        self.customers_tree.column('phone', width=150)
        self.customers_tree.column('added_by', width=150)
        self.customers_tree.column('date_added', width=150)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(self.content_frame, orient='vertical', command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.customers_tree.pack(side='left', fill='both', expand=True, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # تحميل البيانات
        self.refresh_customers_list()

    def refresh_customers_list(self):
        """تحديث قائمة الزبائن"""
        # مسح البيانات الحالية
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        # تحميل البيانات الجديدة
        customers = self.db.get_customers()
        for customer in customers:
            self.customers_tree.insert('', 'end', values=customer)

    def show_add_customer_dialog(self):
        """عرض نافذة إضافة زبون"""
        dialog = ttk_bs.Toplevel(self.root)
        dialog.title(self.translator.get('add_customer'))
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.grab_set()

        # حقل الاسم
        ttk_bs.Label(dialog, text=self.translator.get('customer_name')).pack(pady=5)
        name_entry = ttk_bs.Entry(dialog, width=30, font=('Arial', 12))
        name_entry.pack(pady=5)

        # حقل الهاتف
        ttk_bs.Label(dialog, text=self.translator.get('phone_number')).pack(pady=5)
        phone_entry = ttk_bs.Entry(dialog, width=30, font=('Arial', 12))
        phone_entry.pack(pady=5)

        # حقل الصورة
        ttk_bs.Label(dialog, text=self.translator.get('customer_image')).pack(pady=5)

        image_frame = ttk_bs.Frame(dialog)
        image_frame.pack(pady=5)

        self.selected_image_path = tk.StringVar()
        image_label = ttk_bs.Label(image_frame, textvariable=self.selected_image_path, width=30)
        image_label.pack(side='left', padx=5)

        browse_btn = ttk_bs.Button(
            image_frame,
            text=self.translator.get('browse_image'),
            command=lambda: self.browse_image(self.selected_image_path),
            bootstyle="outline-info"
        )
        browse_btn.pack(side='left', padx=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk_bs.Frame(dialog)
        buttons_frame.pack(pady=20)

        save_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('save'),
            command=lambda: self.save_customer(dialog, name_entry, phone_entry),
            bootstyle="success"
        )
        save_btn.pack(side='left', padx=10)

        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('cancel'),
            command=dialog.destroy,
            bootstyle="secondary"
        )
        cancel_btn.pack(side='left', padx=10)

        name_entry.focus()

    def browse_image(self, image_var):
        """تصفح واختيار صورة"""
        file_path = filedialog.askopenfilename(
            title=self.translator.get('select_image'),
            filetypes=[
                (self.translator.get('image_files'), '*.png *.jpg *.jpeg *.gif *.bmp'),
                ('All files', '*.*')
            ]
        )
        if file_path:
            image_var.set(file_path)

    def save_customer(self, dialog, name_entry, phone_entry):
        """حفظ بيانات الزبون"""
        name = name_entry.get().strip()
        phone = phone_entry.get().strip()
        image_path = self.selected_image_path.get()

        if not name:
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('fill_all_fields')
            )
            return

        try:
            self.db.add_customer(name, phone, image_path, self.current_user)
            messagebox.showinfo(
                self.translator.get('success'),
                self.translator.get('customer_added')
            )
            dialog.destroy()
            self.refresh_customers_list()
        except Exception as e:
            messagebox.showerror(
                self.translator.get('error'),
                str(e)
            )

    def show_services_screen(self):
        """عرض شاشة إدارة الخدمات"""
        self.clear_content_frame()

        # العنوان
        title_label = ttk_bs.Label(
            self.content_frame,
            text=self.translator.get('services'),
            style='Header.TLabel'
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(self.content_frame)
        buttons_frame.pack(fill='x', pady=5)

        if self.current_role == 'admin':  # فقط المدير يمكنه إضافة خدمات
            add_btn = ttk_bs.Button(
                buttons_frame,
                text=self.translator.get('add_service'),
                command=self.show_add_service_dialog,
                bootstyle="success"
            )
            add_btn.pack(side='left', padx=5)

        refresh_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('refresh'),
            command=self.refresh_services_list,
            bootstyle="info"
        )
        refresh_btn.pack(side='left', padx=5)

        # جدول الخدمات
        columns = ('ID', 'name', 'price')
        self.services_tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        self.services_tree.heading('ID', text='ID')
        self.services_tree.heading('name', text=self.translator.get('service_name'))
        self.services_tree.heading('price', text=self.translator.get('price'))

        # تعيين عرض الأعمدة
        self.services_tree.column('ID', width=50)
        self.services_tree.column('name', width=300)
        self.services_tree.column('price', width=150)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(self.content_frame, orient='vertical', command=self.services_tree.yview)
        self.services_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.services_tree.pack(side='left', fill='both', expand=True, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # تحميل البيانات
        self.refresh_services_list()

    def refresh_services_list(self):
        """تحديث قائمة الخدمات"""
        # مسح البيانات الحالية
        for item in self.services_tree.get_children():
            self.services_tree.delete(item)

        # تحميل البيانات الجديدة
        services = self.db.get_services()
        for service in services:
            self.services_tree.insert('', 'end', values=service)

    def show_add_service_dialog(self):
        """عرض نافذة إضافة خدمة"""
        dialog = ttk_bs.Toplevel(self.root)
        dialog.title(self.translator.get('add_service'))
        dialog.geometry("350x200")
        dialog.resizable(False, False)
        dialog.grab_set()

        # حقل اسم الخدمة
        ttk_bs.Label(dialog, text=self.translator.get('service_name')).pack(pady=5)
        name_entry = ttk_bs.Entry(dialog, width=30, font=('Arial', 12))
        name_entry.pack(pady=5)

        # حقل السعر
        ttk_bs.Label(dialog, text=self.translator.get('service_price')).pack(pady=5)
        price_entry = ttk_bs.Entry(dialog, width=30, font=('Arial', 12))
        price_entry.pack(pady=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk_bs.Frame(dialog)
        buttons_frame.pack(pady=20)

        save_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('save'),
            command=lambda: self.save_service(dialog, name_entry, price_entry),
            bootstyle="success"
        )
        save_btn.pack(side='left', padx=10)

        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('cancel'),
            command=dialog.destroy,
            bootstyle="secondary"
        )
        cancel_btn.pack(side='left', padx=10)

        name_entry.focus()

    def save_service(self, dialog, name_entry, price_entry):
        """حفظ بيانات الخدمة"""
        name = name_entry.get().strip()
        price_text = price_entry.get().strip()

        if not name or not price_text:
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('fill_all_fields')
            )
            return

        try:
            price = float(price_text)
            self.db.add_service(name, price)
            messagebox.showinfo(
                self.translator.get('success'),
                self.translator.get('service_added')
            )
            dialog.destroy()
            self.refresh_services_list()
        except ValueError:
            messagebox.showerror(
                self.translator.get('error'),
                "يرجى إدخال سعر صحيح"
            )
        except Exception as e:
            messagebox.showerror(
                self.translator.get('error'),
                str(e)
            )

    def show_operations_screen(self):
        """عرض شاشة العمليات"""
        self.clear_content_frame()

        # العنوان
        title_label = ttk_bs.Label(
            self.content_frame,
            text=self.translator.get('operations'),
            style='Header.TLabel'
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(self.content_frame)
        buttons_frame.pack(fill='x', pady=5)

        add_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('new_operation'),
            command=self.show_add_operation_dialog,
            bootstyle="success"
        )
        add_btn.pack(side='left', padx=5)

        refresh_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('refresh'),
            command=self.refresh_operations_list,
            bootstyle="info"
        )
        refresh_btn.pack(side='left', padx=5)

        # جدول العمليات
        columns = ('ID', 'customer', 'service', 'performed_by', 'customer_owner', 'price', 'employee_share', 'owner_share', 'date')
        self.operations_tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=12)

        # تعيين عناوين الأعمدة
        self.operations_tree.heading('ID', text='ID')
        self.operations_tree.heading('customer', text=self.translator.get('select_customer'))
        self.operations_tree.heading('service', text=self.translator.get('select_service'))
        self.operations_tree.heading('performed_by', text=self.translator.get('performed_by'))
        self.operations_tree.heading('customer_owner', text=self.translator.get('customer_owner'))
        self.operations_tree.heading('price', text=self.translator.get('total_price'))
        self.operations_tree.heading('employee_share', text=self.translator.get('employee_share'))
        self.operations_tree.heading('owner_share', text=self.translator.get('owner_share'))
        self.operations_tree.heading('date', text=self.translator.get('date_added'))

        # تعيين عرض الأعمدة
        self.operations_tree.column('ID', width=50)
        self.operations_tree.column('customer', width=120)
        self.operations_tree.column('service', width=120)
        self.operations_tree.column('performed_by', width=100)
        self.operations_tree.column('customer_owner', width=100)
        self.operations_tree.column('price', width=80)
        self.operations_tree.column('employee_share', width=100)
        self.operations_tree.column('owner_share', width=100)
        self.operations_tree.column('date', width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(self.content_frame, orient='vertical', command=self.operations_tree.yview)
        self.operations_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.operations_tree.pack(side='left', fill='both', expand=True, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # تحميل البيانات
        self.refresh_operations_list()

    def refresh_operations_list(self):
        """تحديث قائمة العمليات"""
        # مسح البيانات الحالية
        for item in self.operations_tree.get_children():
            self.operations_tree.delete(item)

        # تحميل البيانات الجديدة
        operations = self.db.get_operations()
        for operation in operations:
            self.operations_tree.insert('', 'end', values=operation)

    def show_add_operation_dialog(self):
        """عرض نافذة إضافة عملية"""
        dialog = ttk_bs.Toplevel(self.root)
        dialog.title(self.translator.get('new_operation'))
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.grab_set()

        # اختيار الزبون
        ttk_bs.Label(dialog, text=self.translator.get('select_customer')).pack(pady=5)
        customer_var = tk.StringVar()
        customer_combo = ttk_bs.Combobox(dialog, textvariable=customer_var, state='readonly', width=40)

        # تحميل قائمة الزبائن
        customers = self.db.get_customers()
        customer_values = [f"{customer[0]} - {customer[1]}" for customer in customers]
        customer_combo['values'] = customer_values
        customer_combo.pack(pady=5)

        # اختيار الخدمة
        ttk_bs.Label(dialog, text=self.translator.get('select_service')).pack(pady=5)
        service_var = tk.StringVar()
        service_combo = ttk_bs.Combobox(dialog, textvariable=service_var, state='readonly', width=40)

        # تحميل قائمة الخدمات
        services = self.db.get_services()
        service_values = [f"{service[0]} - {service[1]} ({service[2]} ج.م)" for service in services]
        service_combo['values'] = service_values
        service_combo.pack(pady=5)

        # اختيار من نفذ الخدمة
        ttk_bs.Label(dialog, text=self.translator.get('performed_by')).pack(pady=5)
        performed_by_var = tk.StringVar()
        performed_by_combo = ttk_bs.Combobox(dialog, textvariable=performed_by_var, state='readonly', width=40)

        # تحميل قائمة المستخدمين
        users = self.db.get_users()
        user_values = [user[0] for user in users]
        performed_by_combo['values'] = user_values
        performed_by_combo.pack(pady=5)

        # اختيار صاحب الزبون
        ttk_bs.Label(dialog, text=self.translator.get('customer_owner')).pack(pady=5)
        customer_owner_var = tk.StringVar()
        customer_owner_combo = ttk_bs.Combobox(dialog, textvariable=customer_owner_var, state='readonly', width=40)
        customer_owner_combo['values'] = user_values
        customer_owner_combo.pack(pady=5)

        # عرض السعر والأرباح
        price_frame = ttk_bs.LabelFrame(dialog, text=self.translator.get('total_price'), padding=10)
        price_frame.pack(pady=10, padx=20, fill='x')

        price_label = ttk_bs.Label(price_frame, text="0.00 ج.م", font=('Arial', 14, 'bold'))
        price_label.pack()

        employee_share_label = ttk_bs.Label(price_frame, text=f"{self.translator.get('employee_share')}: 0.00 ج.م")
        employee_share_label.pack()

        owner_share_label = ttk_bs.Label(price_frame, text=f"{self.translator.get('owner_share')}: 0.00 ج.م")
        owner_share_label.pack()

        def update_price_calculation(*args):
            """تحديث حساب الأرباح"""
            try:
                service_text = service_var.get()
                performed_by = performed_by_var.get()
                customer_owner = customer_owner_var.get()

                if service_text and performed_by and customer_owner:
                    # استخراج السعر من النص
                    price_start = service_text.rfind('(') + 1
                    price_end = service_text.rfind(' ج.م)')
                    price = float(service_text[price_start:price_end])

                    # حساب الأرباح
                    if performed_by == customer_owner:
                        employee_share = price
                        owner_share = 0
                    else:
                        employee_share = price * 0.5
                        owner_share = price * 0.5

                    price_label.config(text=f"{price:.2f} ج.م")
                    employee_share_label.config(text=f"{self.translator.get('employee_share')}: {employee_share:.2f} ج.م")
                    owner_share_label.config(text=f"{self.translator.get('owner_share')}: {owner_share:.2f} ج.م")
            except:
                pass

        # ربط التحديث بتغيير القيم
        service_var.trace('w', update_price_calculation)
        performed_by_var.trace('w', update_price_calculation)
        customer_owner_var.trace('w', update_price_calculation)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk_bs.Frame(dialog)
        buttons_frame.pack(pady=20)

        save_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('record_operation'),
            command=lambda: self.save_operation(dialog, customer_var, service_var, performed_by_var, customer_owner_var),
            bootstyle="success"
        )
        save_btn.pack(side='left', padx=10)

        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text=self.translator.get('cancel'),
            command=dialog.destroy,
            bootstyle="secondary"
        )
        cancel_btn.pack(side='left', padx=10)

    def save_operation(self, dialog, customer_var, service_var, performed_by_var, customer_owner_var):
        """حفظ بيانات العملية"""
        customer_text = customer_var.get()
        service_text = service_var.get()
        performed_by = performed_by_var.get()
        customer_owner = customer_owner_var.get()

        if not all([customer_text, service_text, performed_by, customer_owner]):
            messagebox.showerror(
                self.translator.get('error'),
                self.translator.get('fill_all_fields')
            )
            return

        try:
            # استخراج معرف الزبون
            customer_id = int(customer_text.split(' - ')[0])

            # استخراج معرف الخدمة والسعر
            service_id = int(service_text.split(' - ')[0])
            price_start = service_text.rfind('(') + 1
            price_end = service_text.rfind(' ج.م)')
            price = float(service_text[price_start:price_end])

            # حفظ العملية
            self.db.add_operation(customer_id, service_id, performed_by, customer_owner, price)

            messagebox.showinfo(
                self.translator.get('success'),
                self.translator.get('operation_recorded')
            )
            dialog.destroy()
            self.refresh_operations_list()

        except Exception as e:
            messagebox.showerror(
                self.translator.get('error'),
                str(e)
            )

    def show_reports_screen(self):
        """عرض شاشة التقارير"""
        self.clear_content_frame()

        # العنوان
        title_label = ttk_bs.Label(
            self.content_frame,
            text=self.translator.get('reports'),
            style='Header.TLabel'
        )
        title_label.pack(pady=10)

        # إطار التصفية
        filter_frame = ttk_bs.LabelFrame(self.content_frame, text="تصفية التقارير", padding=10)
        filter_frame.pack(fill='x', pady=10, padx=20)

        # تاريخ البداية
        date_frame = ttk_bs.Frame(filter_frame)
        date_frame.pack(fill='x', pady=5)

        ttk_bs.Label(date_frame, text=self.translator.get('from_date')).pack(side='left', padx=5)
        self.start_date_var = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        start_date_entry = ttk_bs.Entry(date_frame, textvariable=self.start_date_var, width=15)
        start_date_entry.pack(side='left', padx=5)

        ttk_bs.Label(date_frame, text=self.translator.get('to_date')).pack(side='left', padx=5)
        self.end_date_var = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        end_date_entry = ttk_bs.Entry(date_frame, textvariable=self.end_date_var, width=15)
        end_date_entry.pack(side='left', padx=5)

        # اختيار المستخدم (للمدير فقط)
        if self.current_role == 'admin':
            ttk_bs.Label(date_frame, text="المستخدم:").pack(side='left', padx=5)
            self.user_filter_var = tk.StringVar()
            user_combo = ttk_bs.Combobox(date_frame, textvariable=self.user_filter_var, width=15)
            users = self.db.get_users()
            user_values = ['الكل'] + [user[0] for user in users]
            user_combo['values'] = user_values
            user_combo.set('الكل')
            user_combo.pack(side='left', padx=5)

        # زر إنشاء التقرير
        generate_btn = ttk_bs.Button(
            filter_frame,
            text=self.translator.get('generate_report'),
            command=self.generate_report,
            bootstyle="info"
        )
        generate_btn.pack(side='left', padx=10)

        # إطار النتائج
        self.results_frame = ttk_bs.Frame(self.content_frame)
        self.results_frame.pack(fill='both', expand=True, pady=10, padx=20)

        # إنشاء تقرير افتراضي
        self.generate_report()

    def generate_report(self):
        """إنشاء التقرير"""
        # مسح النتائج السابقة
        for widget in self.results_frame.winfo_children():
            widget.destroy()

        start_date = self.start_date_var.get()
        end_date = self.end_date_var.get()

        # تحديد المستخدم للتصفية
        user_filter = None
        if self.current_role == 'admin' and hasattr(self, 'user_filter_var'):
            if self.user_filter_var.get() != 'الكل':
                user_filter = self.user_filter_var.get()
        elif self.current_role == 'employee':
            user_filter = self.current_user

        # الحصول على البيانات
        operations = self.db.get_operations(start_date, end_date, user_filter)

        # حساب الإحصائيات
        total_operations = len(operations)
        total_revenue = sum(op[5] for op in operations)  # السعر الإجمالي
        total_employee_share = sum(op[6] for op in operations)  # نصيب العامل
        total_owner_share = sum(op[7] for op in operations)  # نصيب المالك

        # عرض الإحصائيات
        stats_frame = ttk_bs.LabelFrame(self.results_frame, text="الإحصائيات", padding=10)
        stats_frame.pack(fill='x', pady=5)

        stats_text = f"""
        {self.translator.get('total_operations')}: {total_operations}
        {self.translator.get('total_revenue')}: {total_revenue:.2f} ج.م
        {self.translator.get('employee_earnings')}: {total_employee_share:.2f} ج.م
        {self.translator.get('owner_earnings')}: {total_owner_share:.2f} ج.م
        """

        ttk_bs.Label(stats_frame, text=stats_text, font=('Arial', 12)).pack()

        # جدول العمليات
        operations_frame = ttk_bs.LabelFrame(self.results_frame, text="تفاصيل العمليات", padding=10)
        operations_frame.pack(fill='both', expand=True, pady=5)

        columns = ('customer', 'service', 'performed_by', 'price', 'employee_share', 'date')
        operations_tree = ttk.Treeview(operations_frame, columns=columns, show='headings', height=10)

        # تعيين عناوين الأعمدة
        operations_tree.heading('customer', text='الزبون')
        operations_tree.heading('service', text='الخدمة')
        operations_tree.heading('performed_by', text='نفذها')
        operations_tree.heading('price', text='السعر')
        operations_tree.heading('employee_share', text='نصيب العامل')
        operations_tree.heading('date', text='التاريخ')

        # تعيين عرض الأعمدة
        operations_tree.column('customer', width=150)
        operations_tree.column('service', width=150)
        operations_tree.column('performed_by', width=100)
        operations_tree.column('price', width=100)
        operations_tree.column('employee_share', width=100)
        operations_tree.column('date', width=120)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(operations_frame, orient='vertical', command=operations_tree.yview)
        operations_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة البيانات
        for operation in operations:
            operations_tree.insert('', 'end', values=(
                operation[1],  # اسم الزبون
                operation[2],  # اسم الخدمة
                operation[3],  # من نفذها
                f"{operation[5]:.2f}",  # السعر
                f"{operation[6]:.2f}",  # نصيب العامل
                operation[8][:10]  # التاريخ
            ))

        operations_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # أزرار التصدير
        export_frame = ttk_bs.Frame(self.results_frame)
        export_frame.pack(fill='x', pady=10)

        pdf_btn = ttk_bs.Button(
            export_frame,
            text=self.translator.get('export_pdf'),
            command=lambda: self.export_report('pdf', operations, {
                'total_operations': total_operations,
                'total_revenue': total_revenue,
                'employee_earnings': total_employee_share,
                'owner_earnings': total_owner_share
            }),
            bootstyle="danger"
        )
        pdf_btn.pack(side='left', padx=5)

        excel_btn = ttk_bs.Button(
            export_frame,
            text=self.translator.get('export_excel'),
            command=lambda: self.export_report('excel', operations, {
                'total_operations': total_operations,
                'total_revenue': total_revenue,
                'employee_earnings': total_employee_share,
                'owner_earnings': total_owner_share
            }),
            bootstyle="success"
        )
        excel_btn.pack(side='left', padx=5)

    def export_report(self, format_type, operations, stats):
        """تصدير التقرير"""
        try:
            from reports import ReportGenerator

            # اختيار مكان الحفظ
            if format_type == 'pdf':
                filename = filedialog.asksaveasfilename(
                    defaultextension=".pdf",
                    filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                    title="حفظ التقرير كـ PDF"
                )
            else:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                    title="حفظ التقرير كـ Excel"
                )

            if filename:
                report_gen = ReportGenerator(self.translator)

                if format_type == 'pdf':
                    success = report_gen.export_to_pdf(operations, stats, filename)
                else:
                    success = report_gen.export_to_excel(operations, stats, filename)

                if success:
                    messagebox.showinfo(
                        self.translator.get('success'),
                        f"تم تصدير التقرير بنجاح إلى:\n{filename}"
                    )
                else:
                    messagebox.showerror(
                        self.translator.get('error'),
                        "فشل في تصدير التقرير"
                    )
        except ImportError:
            messagebox.showerror(
                self.translator.get('error'),
                "مكتبات التصدير غير متوفرة. يرجى تثبيت reportlab و openpyxl"
            )
        except Exception as e:
            messagebox.showerror(
                self.translator.get('error'),
                f"خطأ في التصدير: {str(e)}"
            )

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CoiffeurApp()
    app.run()
