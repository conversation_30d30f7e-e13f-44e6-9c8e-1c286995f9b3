#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص سلامة تثبيت برنامج إدارة الحلاقة
يتحقق من وجود جميع المكتبات والملفات المطلوبة
"""

import sys
import os
import importlib
from version import __version__, SYSTEM_REQUIREMENTS

def check_python_version():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    
    current_version = sys.version_info
    required_version = (3, 8)
    
    if current_version >= required_version:
        print(f"✅ Python {current_version.major}.{current_version.minor}.{current_version.micro} - مناسب")
        return True
    else:
        print(f"❌ Python {current_version.major}.{current_version.minor} - يتطلب Python 3.8 أو أحدث")
        return False

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("\n🔍 فحص المكتبات المطلوبة...")
    
    dependencies = SYSTEM_REQUIREMENTS["dependencies"]
    missing_deps = []
    
    for dep in dependencies:
        # استخراج اسم المكتبة (قبل >=)
        package_name = dep.split('>=')[0]
        
        # تحويل أسماء المكتبات الخاصة
        import_name = package_name
        if package_name == 'python-bidi':
            import_name = 'bidi'
        elif package_name == 'arabic-reshaper':
            import_name = 'arabic_reshaper'
        elif package_name == 'Pillow':
            import_name = 'PIL'
        
        try:
            importlib.import_module(import_name)
            print(f"✅ {package_name} - متوفر")
        except ImportError:
            print(f"❌ {package_name} - مفقود")
            missing_deps.append(package_name)
    
    if missing_deps:
        print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing_deps)}")
        print("لتثبيت المكتبات المفقودة، شغل:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    else:
        print("\n✅ جميع المكتبات متوفرة")
        return True

def check_required_files():
    """فحص الملفات المطلوبة"""
    print("\n🔍 فحص الملفات المطلوبة...")
    
    required_files = [
        'main.py',
        'database.py',
        'translations.py',
        'reports.py',
        'config.py',
        'version.py',
        'requirements.txt'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - موجود")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  الملفات المفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات موجودة")
        return True

def check_database_creation():
    """فحص إمكانية إنشاء قاعدة البيانات"""
    print("\n🔍 فحص إمكانية إنشاء قاعدة البيانات...")
    
    try:
        from database import Database
        
        # إنشاء قاعدة بيانات مؤقتة
        test_db = Database("test_check.db")
        
        # اختبار إضافة مستخدم
        success = test_db.add_user("test_user", role="employee")
        
        if success:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            
            # حذف قاعدة البيانات المؤقتة
            if os.path.exists("test_check.db"):
                os.remove("test_check.db")
            
            return True
        else:
            print("❌ مشكلة في قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def check_translations():
    """فحص نظام الترجمات"""
    print("\n🔍 فحص نظام الترجمات...")
    
    try:
        from translations import Translator
        
        # اختبار الترجمة العربية
        ar_translator = Translator('ar')
        ar_text = ar_translator.get('login')
        
        # اختبار الترجمة الفرنسية
        fr_translator = Translator('fr')
        fr_text = fr_translator.get('login')
        
        if ar_text == 'دخول' and fr_text == 'Connexion':
            print("✅ نظام الترجمات يعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في نظام الترجمات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في نظام الترجمات: {e}")
        return False

def check_config_system():
    """فحص نظام الإعدادات"""
    print("\n🔍 فحص نظام الإعدادات...")
    
    try:
        from config import Config
        
        # إنشاء إعدادات مؤقتة
        test_config = Config("test_config.json")
        
        # اختبار الحصول على إعداد
        language = test_config.get_language()
        
        # اختبار تعيين إعداد
        test_config.set('ui', 'test_key', 'test_value')
        
        if language and test_config.get('ui', 'test_key') == 'test_value':
            print("✅ نظام الإعدادات يعمل بشكل صحيح")
            
            # حذف ملف الإعدادات المؤقت
            if os.path.exists("test_config.json"):
                os.remove("test_config.json")
            
            return True
        else:
            print("❌ مشكلة في نظام الإعدادات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في نظام الإعدادات: {e}")
        return False

def check_gui_libraries():
    """فحص مكتبات الواجهة الرسومية"""
    print("\n🔍 فحص مكتبات الواجهة الرسومية...")
    
    try:
        import tkinter
        import ttkbootstrap
        
        # اختبار إنشاء نافذة بسيطة
        root = ttkbootstrap.Window()
        root.withdraw()  # إخفاء النافذة
        root.destroy()
        
        print("✅ مكتبات الواجهة الرسومية تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مكتبات الواجهة الرسومية: {e}")
        return False

def run_full_check():
    """تشغيل فحص شامل"""
    print("=" * 60)
    print(f"🔧 فحص سلامة تثبيت برنامج إدارة الحلاقة - الإصدار {__version__}")
    print("=" * 60)
    
    checks = [
        ("إصدار Python", check_python_version),
        ("المكتبات المطلوبة", check_dependencies),
        ("الملفات المطلوبة", check_required_files),
        ("قاعدة البيانات", check_database_creation),
        ("نظام الترجمات", check_translations),
        ("نظام الإعدادات", check_config_system),
        ("مكتبات الواجهة", check_gui_libraries)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_function in checks:
        try:
            if check_function():
                passed_checks += 1
        except Exception as e:
            print(f"❌ خطأ في فحص {check_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الفحص: {passed_checks}/{total_checks} اختبار نجح")
    
    if passed_checks == total_checks:
        print("🎉 البرنامج جاهز للتشغيل!")
        print("يمكنك الآن تشغيل البرنامج باستخدام: python run.py")
    else:
        print("⚠️  يرجى حل المشاكل المذكورة أعلاه قبل تشغيل البرنامج")
        print("للمساعدة، راجع ملف README.md")
    
    print("=" * 60)
    
    return passed_checks == total_checks

if __name__ == "__main__":
    run_full_check()
