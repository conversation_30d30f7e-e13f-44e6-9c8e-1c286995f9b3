#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظيف لبرنامج إدارة الحلاقة
بدون رسائل تحذير أو فحوصات إضافية
"""

import sys
import os

def main():
    """تشغيل البرنامج مباشرة"""
    try:
        # إخفاء رسائل التحذير
        import warnings
        warnings.filterwarnings("ignore")
        
        # تشغيل البرنامج مباشرة
        from main import CoiffeurApp
        
        app = CoiffeurApp()
        app.run()
        
    except ImportError as e:
        # في حالة مكتبة مفقودة، عرض رسالة بسيطة
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()
        
        messagebox.showerror(
            "خطأ", 
            f"مكتبة مفقودة: {str(e)}\n\n"
            "يرجى تشغيل:\n"
            "pip install ttkbootstrap Pillow reportlab openpyxl python-bidi arabic-reshaper"
        )
        
    except Exception as e:
        # في حالة أي خطأ آخر
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()
        
        messagebox.showerror(
            "خطأ في التشغيل",
            f"حدث خطأ: {str(e)}\n\n"
            "يرجى التأكد من:\n"
            "1. تثبيت جميع المكتبات المطلوبة\n"
            "2. وجود جميع ملفات البرنامج"
        )

if __name__ == "__main__":
    main()
