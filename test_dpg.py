#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Dear PyGui - نسخة مبسطة
"""

import dearpygui.dearpygui as dpg

def main():
    """الدالة الرئيسية"""
    # إنشاء السياق
    dpg.create_context()
    
    # إنشاء النافذة الرئيسية
    dpg.create_viewport(
        title="💈 اختبار Dear PyGui",
        width=800,
        height=600
    )
    
    # إنشاء النافذة
    with dpg.window(label="اختبار", tag="main_window"):
        dpg.add_text("مرحباً بك في Dear PyGui!")
        dpg.add_text("هذا اختبار للتأكد من عمل المكتبة")
        
        dpg.add_separator()
        
        # بطاقة اختبار
        with dpg.child_window(width=300, height=200, border=True):
            dpg.add_text("🃏 بطاقة اختبار")
            dpg.add_separator()
            dpg.add_text("هذه بطاقة للاختبار")
            dpg.add_button(label="زر اختبار", callback=lambda: print("تم النقر!"))
        
        dpg.add_spacing(count=2)
        dpg.add_button(label="إغلاق", callback=lambda: dpg.stop_dearpygui())
    
    # تعيين النافذة الرئيسية
    dpg.set_primary_window("main_window", True)
    
    # تشغيل التطبيق
    dpg.setup_dearpygui()
    dpg.show_viewport()
    dpg.start_dearpygui()
    dpg.destroy_context()

if __name__ == "__main__":
    main()
