#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لبرنامج إدارة الحلاقة
للتأكد من عمل المكونات الأساسية
"""

import os
import tempfile

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        import tkinter
        print("✅ tkinter - متوفر")
    except ImportError:
        print("❌ tkinter - مفقود")
        return False
    
    try:
        import ttkbootstrap
        print("✅ ttkbootstrap - متوفر")
    except ImportError:
        print("❌ ttkbootstrap - مفقود")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow - متوفر")
    except ImportError:
        print("❌ Pillow - مفقود")
        return False
    
    try:
        import sqlite3
        print("✅ sqlite3 - متوفر")
    except ImportError:
        print("❌ sqlite3 - مفقود")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🔍 اختبار قاعدة البيانات...")
    
    try:
        from database import Database
        
        # إنشاء قاعدة بيانات مؤقتة
        test_db_path = tempfile.mktemp(suffix='.db')
        db = Database(test_db_path)
        
        # اختبار تسجيل الدخول للمدير
        result = db.authenticate_user('admin', 'admin123')
        if result and result[1] == 'admin':
            print("✅ تسجيل دخول المدير - يعمل")
        else:
            print("❌ تسجيل دخول المدير - لا يعمل")
            return False
        
        # اختبار إضافة زبون
        customer_id = db.add_customer('زبون تجريبي', '01234567890', None, 'admin')
        if customer_id:
            print("✅ إضافة زبون - يعمل")
        else:
            print("❌ إضافة زبون - لا يعمل")
            return False
        
        # اختبار إضافة خدمة
        db.add_service('خدمة تجريبية', 50.0)
        services = db.get_services()
        if any(s[1] == 'خدمة تجريبية' for s in services):
            print("✅ إضافة خدمة - يعمل")
        else:
            print("❌ إضافة خدمة - لا يعمل")
            return False
        
        # تنظيف
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_translations():
    """اختبار نظام الترجمات"""
    print("\n🔍 اختبار نظام الترجمات...")
    
    try:
        from translations import Translator
        
        # اختبار الترجمة العربية
        ar_translator = Translator('ar')
        if ar_translator.get('login') == 'دخول':
            print("✅ الترجمة العربية - تعمل")
        else:
            print("❌ الترجمة العربية - لا تعمل")
            return False
        
        # اختبار الترجمة الفرنسية
        fr_translator = Translator('fr')
        if fr_translator.get('login') == 'Connexion':
            print("✅ الترجمة الفرنسية - تعمل")
        else:
            print("❌ الترجمة الفرنسية - لا تعمل")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام الترجمات: {e}")
        return False

def test_config():
    """اختبار نظام الإعدادات"""
    print("\n🔍 اختبار نظام الإعدادات...")
    
    try:
        from config import Config
        
        # إنشاء إعدادات مؤقتة
        test_config_path = tempfile.mktemp(suffix='.json')
        config = Config(test_config_path)
        
        # اختبار الحصول على إعداد
        if config.get_language() in ['ar', 'fr']:
            print("✅ قراءة الإعدادات - تعمل")
        else:
            print("❌ قراءة الإعدادات - لا تعمل")
            return False
        
        # اختبار تعيين إعداد
        config.set('test', 'key', 'value')
        if config.get('test', 'key') == 'value':
            print("✅ كتابة الإعدادات - تعمل")
        else:
            print("❌ كتابة الإعدادات - لا تعمل")
            return False
        
        # تنظيف
        if os.path.exists(test_config_path):
            os.remove(test_config_path)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام الإعدادات: {e}")
        return False

def test_gui():
    """اختبار الواجهة الرسومية"""
    print("\n🔍 اختبار الواجهة الرسومية...")
    
    try:
        import ttkbootstrap as ttk_bs
        
        # إنشاء نافذة تجريبية
        root = ttk_bs.Window()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء عنصر تجريبي
        label = ttk_bs.Label(root, text="اختبار")
        
        # إغلاق النافذة
        root.destroy()
        
        print("✅ الواجهة الرسومية - تعمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة الرسومية: {e}")
        return False

def test_reports():
    """اختبار نظام التقارير"""
    print("\n🔍 اختبار نظام التقارير...")
    
    try:
        # اختبار استيراد مكتبات التقارير
        import reportlab
        import openpyxl
        print("✅ مكتبات التقارير - متوفرة")
        
        # اختبار استيراد مولد التقارير
        from reports import ReportGenerator
        from translations import Translator
        
        translator = Translator('ar')
        report_gen = ReportGenerator(translator)
        print("✅ مولد التقارير - يعمل")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبات التقارير مفقودة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في نظام التقارير: {e}")
        return False

def run_simple_tests():
    """تشغيل جميع الاختبارات المبسطة"""
    print("=" * 60)
    print("🧪 اختبارات مبسطة لبرنامج إدارة الحلاقة")
    print("=" * 60)
    
    tests = [
        ("استيراد المكتبات", test_imports),
        ("قاعدة البيانات", test_database),
        ("نظام الترجمات", test_translations),
        ("نظام الإعدادات", test_config),
        ("الواجهة الرسومية", test_gui),
        ("نظام التقارير", test_reports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للتشغيل.")
        print("يمكنك الآن تشغيل البرنامج باستخدام: python run.py")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى حل المشاكل قبل تشغيل البرنامج.")
        print("للمساعدة، شغل: python check_installation.py")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    run_simple_tests()
