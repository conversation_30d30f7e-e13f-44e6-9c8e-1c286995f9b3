@echo off
title تثبيت مكتبات برنامج الحلاقة
echo ================================
echo   تثبيت مكتبات برنامج الحلاقة
echo ================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من python.org
    pause
    exit /b 1
)

echo تثبيت المكتبات المطلوبة...
echo.

REM ترقية pip أولاً
echo ترقية pip...
python -m pip install --upgrade pip

echo.
echo تثبيت المكتبات...

REM تثبيت المكتبات واحدة تلو الأخرى مع عرض التقدم
echo [1/6] تثبيت ttkbootstrap...
pip install ttkbootstrap==1.10.1

echo [2/6] تثبيت Pillow...
pip install Pillow==10.0.0

echo [3/6] تثبيت reportlab...
pip install reportlab==4.0.4

echo [4/6] تثبيت openpyxl...
pip install openpyxl==3.1.2

echo [5/6] تثبيل python-bidi...
pip install python-bidi==0.4.2

echo [6/6] تثبيت arabic-reshaper...
pip install arabic-reshaper==3.0.0

echo.
echo ================================
echo تم تثبيت جميع المكتبات بنجاح!
echo يمكنك الآن تشغيل البرنامج
echo ================================
echo.
pause
